<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Chapter;
use App\Models\Subject;
use Illuminate\Http\Request;

class ChapterController extends Controller
{
    /**
     * Hiển Thị <PERSON> Sách Chương
     */
    public function index()
    {
        $chapters = Chapter::with(['subject', 'lessons'])
            ->withCount('lessons')
            ->orderBy('subject_id')
            ->orderBy('order')
            ->get();

        return view('admin.chapters.index', compact('chapters'));
    }

    /**
     * Hiển Thị Form Tạo Chương Mới
     */
    public function create()
    {
        $subjects = Subject::orderBy('title')->get();
        return view('admin.chapters.create', compact('subjects'));
    }

    /**
     * Lưu Chương Mới
     */
    public function store(Request $request)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'title' => 'required|string|max:255',
            'order' => 'nullable|integer|min:0',
        ]);

        $data = $request->only(['subject_id', 'title', 'order']);

        // Nếu không có order, tự động tăng
        if (!$data['order']) {
            $data['order'] = Chapter::where('subject_id', $data['subject_id'])->max('order') + 1;
        }

        Chapter::create($data);

        return redirect()->route('admin.chapters.index')
            ->with('success', 'Chương Đã Được Tạo Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Chương
     */
    public function show(Chapter $chapter)
    {
        $chapter->load(['subject', 'lessons' => function($query) {
            $query->orderBy('order');
        }]);

        return view('admin.chapters.show', compact('chapter'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa Chương
     */
    public function edit(Chapter $chapter)
    {
        $subjects = Subject::orderBy('title')->get();
        return view('admin.chapters.edit', compact('chapter', 'subjects'));
    }

    /**
     * Cập Nhật Chương
     */
    public function update(Request $request, Chapter $chapter)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'title' => 'required|string|max:255',
            'order' => 'nullable|integer|min:0',
        ]);

        $data = $request->only(['subject_id', 'title', 'order']);

        // Nếu không có order, giữ nguyên order hiện tại
        if (!$data['order']) {
            $data['order'] = $chapter->order;
        }

        $chapter->update($data);

        return redirect()->route('admin.chapters.index')
            ->with('success', 'Chương Đã Được Cập Nhật Thành Công!');
    }

    /**
     * Xóa Chương
     */
    public function destroy(Chapter $chapter)
    {
        $chapter->delete();

        return redirect()->route('admin.chapters.index')
            ->with('success', 'Chương Đã Được Xóa Thành Công!');
    }
}
