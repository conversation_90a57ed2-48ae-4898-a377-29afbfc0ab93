<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Routes cho User
    Route::prefix('user')->name('user.')->group(function () {
        // Tất Cả Khóa Học
        Route::get('all-subjects', [\App\Http\Controllers\User\AllSubjectsController::class, 'index'])->name('all-subjects.index');

        // Môn Học
        Route::get('subjects', [\App\Http\Controllers\User\SubjectController::class, 'index'])->name('subjects.index');
        Route::get('subjects/{subject}', [\App\Http\Controllers\User\SubjectController::class, 'show'])->middleware('subject.access')->name('subjects.show');
        Route::get('subjects/{subject}/chapters/{chapter}/lessons/{lesson}', [\App\Http\Controllers\User\SubjectController::class, 'lesson'])->middleware('subject.access')->name('subjects.lesson');

        // Mua Môn Học
        Route::get('subjects/{subject}/purchase', [\App\Http\Controllers\User\SubjectPurchaseController::class, 'show'])->name('subjects.purchase');
        Route::post('subjects/{subject}/purchase', [\App\Http\Controllers\User\SubjectPurchaseController::class, 'purchaseWithBalance'])->name('subjects.purchase.balance');
        Route::get('purchase-history', [\App\Http\Controllers\User\SubjectPurchaseController::class, 'purchaseHistory'])->name('subjects.purchase.history');

        // Tiến Độ Học Tập
        Route::get('progress', [\App\Http\Controllers\User\ProgressController::class, 'index'])->name('progress.index');

        // Tài Liệu
        Route::get('documents', [\App\Http\Controllers\User\DocumentController::class, 'index'])->name('documents.index');
        Route::get('documents/{lesson}/download', [\App\Http\Controllers\User\DocumentController::class, 'download'])->name('documents.download');

        // Comments
        Route::post('lessons/{lesson}/comments', [\App\Http\Controllers\User\LessonCommentController::class, 'store'])->name('lessons.comments.store');
        Route::delete('comments/{comment}', [\App\Http\Controllers\User\LessonCommentController::class, 'destroy'])->name('comments.destroy');

        // Progress
        Route::post('lessons/{lesson}/progress/complete', [\App\Http\Controllers\User\LessonProgressController::class, 'markCompleted'])->name('lessons.progress.complete');
        Route::post('lessons/{lesson}/progress/incomplete', [\App\Http\Controllers\User\LessonProgressController::class, 'markIncomplete'])->name('lessons.progress.incomplete');
        Route::post('lessons/{lesson}/progress/watch-time', [\App\Http\Controllers\User\LessonProgressController::class, 'updateWatchTime'])->name('lessons.progress.watch-time');

        // Bot Management
        Route::get('bots', [\App\Http\Controllers\User\BotController::class, 'index'])->name('bots.index');
        Route::get('bots/guide', [\App\Http\Controllers\User\BotController::class, 'guide'])->name('bots.guide');
    });
});

// Route Admin - Chỉ Admin Mới Truy Cập Được
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    // CRUD Môn Học
    Route::resource('subjects', \App\Http\Controllers\Admin\SubjectController::class);

    // CRUD Chương
    Route::resource('chapters', \App\Http\Controllers\Admin\ChapterController::class);

    // CRUD Bài Học
    Route::resource('lessons', \App\Http\Controllers\Admin\LessonController::class);

    // CRUD Người Dùng
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);

    // Toggle Admin Status
    Route::patch('users/{user}/toggle-admin', [\App\Http\Controllers\Admin\UserController::class, 'toggleAdmin'])
        ->name('users.toggle-admin');

    // Thống Kê
    Route::get('statistics', [\App\Http\Controllers\Admin\StatisticsController::class, 'index'])
        ->name('statistics.index');
    Route::get('statistics/chart-data', [\App\Http\Controllers\Admin\StatisticsController::class, 'chartData'])
        ->name('statistics.chart-data');

    // Settings
    Route::get('settings', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings.index');
    Route::put('settings', [\App\Http\Controllers\Admin\SettingController::class, 'update'])->name('settings.update');
    Route::get('settings/initialize', [\App\Http\Controllers\Admin\SettingController::class, 'initializeDefaults'])->name('settings.initialize');

    // Bot Management
    Route::get('bots', [\App\Http\Controllers\Admin\BotController::class, 'index'])->name('bots.index');
    Route::put('bots/{subject}', [\App\Http\Controllers\Admin\BotController::class, 'updateSubjectBot'])->name('bots.update');
    Route::get('bots/{subject}/test-telegram', [\App\Http\Controllers\Admin\BotController::class, 'testTelegram'])->name('bots.test-telegram');
    Route::get('bots/{subject}/test-discord', [\App\Http\Controllers\Admin\BotController::class, 'testDiscord'])->name('bots.test-discord');
    Route::post('bots/{subject}/send-notification', [\App\Http\Controllers\Admin\BotController::class, 'sendManualNotification'])->name('bots.send-notification');
    Route::get('bots/{subject}/send-weekly-stats', [\App\Http\Controllers\Admin\BotController::class, 'sendWeeklyStats'])->name('bots.send-weekly-stats');
});

require __DIR__.'/auth.php';
