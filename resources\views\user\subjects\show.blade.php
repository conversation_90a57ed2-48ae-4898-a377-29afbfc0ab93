<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
            <div style="display: flex; align-items: center; gap: 12px;">
                <a href="{{ route('user.subjects.index') }}"
                   style="background-color: #f3f4f6; color: #374151; padding: 8px 12px; border-radius: 8px; text-decoration: none; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                   onmouseout="this.style.backgroundColor='#f3f4f6'">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                        {{ $subject->title }}
                    </h2>
                    <p style="color: #6b7280; margin: 4px 0 0 0; font-size: 0.875rem;">
                        <i class="fas fa-bookmark" style="margin-right: 4px;"></i>
                        {{ $subject->chapters->count() }} Chương • {{ $subject->chapters->sum(function($chapter) { return $chapter->lessons->count(); }) }} Bài Học
                    </p>
                </div>
            </div>
            @if($subject->group_link)
                <a href="{{ $subject->group_link }}"
                   target="_blank"
                   style="background-color: #0088cc; color: #ffffff; padding: 10px 16px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#006699'"
                   onmouseout="this.style.backgroundColor='#0088cc'">
                    <i class="fab fa-telegram" style="margin-right: 6px;"></i>
                    Nhóm Telegram
                </a>
            @endif
        </div>
    </x-slot>

    <!-- Mobile Responsive CSS -->
    <style>
        @media (max-width: 768px) {
            .mobile-grid { grid-template-columns: 1fr !important; }
            .mobile-text-sm { font-size: 0.875rem !important; }
            .mobile-p-sm { padding: 16px !important; }
            .mobile-gap-sm { gap: 12px !important; }
            .mobile-stack { flex-direction: column !important; align-items: flex-start !important; }
            .mobile-hidden { display: none !important; }
        }
    </style>

    <!-- Subject Info -->
    @if($subject->description)
        <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px; margin-bottom: 24px;" class="mobile-p-sm">
            <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 12px 0;">
                <i class="fas fa-info-circle" style="margin-right: 8px; color: #2563eb;"></i>
                Giới Thiệu Môn Học
            </h3>
            <p style="color: #6b7280; margin: 0; line-height: 1.6;">
                {{ $subject->description }}
            </p>
        </div>
    @endif

    <!-- Purchase Status & Action -->
    @if(!$subject->isFree())
        <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px; margin-bottom: 24px;" class="mobile-p-sm">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;" class="mobile-stack">
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                        <i class="fas fa-credit-card" style="margin-right: 8px; color: #2563eb;"></i>
                        Thông Tin Thanh Toán
                    </h3>
                    <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
                        <span style="color: #374151; font-weight: 500;">Giá:</span>
                        <span style="color: #dc2626; font-weight: bold; font-size: 1.25rem;">{{ $subject->formatted_price }}</span>

                        @if($hasAccess)
                            <span style="background-color: #dcfce7; color: #166534; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 600;">
                                <i class="fas fa-check-circle" style="margin-right: 4px;"></i>
                                Đã Mua
                            </span>
                        @else
                            <span style="background-color: #fef3c7; color: #d97706; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 600;">
                                <i class="fas fa-lock" style="margin-right: 4px;"></i>
                                Chưa Mua
                            </span>
                        @endif
                    </div>

                    @if($purchaseInfo)
                        <div style="margin-top: 12px; padding: 12px; background-color: #f8fafc; border-radius: 8px;">
                            <p style="color: #4b5563; margin: 0; font-size: 0.875rem;">
                                <i class="fas fa-calendar" style="margin-right: 4px;"></i>
                                Đã Mua Vào: {{ $purchaseInfo->purchased_at->format('d/m/Y H:i') }}
                            </p>
                            @if($purchaseInfo->expires_at)
                                <p style="color: #4b5563; margin: 4px 0 0 0; font-size: 0.875rem;">
                                    <i class="fas fa-clock" style="margin-right: 4px;"></i>
                                    Hết Hạn: {{ $purchaseInfo->expires_at->format('d/m/Y H:i') }}
                                </p>
                            @endif
                        </div>
                    @endif
                </div>

                @if(!$hasAccess)
                    <div>
                        <a href="{{ route('user.subjects.purchase', $subject) }}"
                           style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; display: inline-block;"
                           onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 15px rgba(102, 126, 234, 0.4)'"
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                            <i class="fas fa-shopping-cart" style="margin-right: 8px;"></i>
                            Mua Ngay
                        </a>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Chapters List -->
    @if($subject->chapters->count() > 0)
        <div style="display: grid; gap: 20px;" class="mobile-gap-sm">
            @foreach($subject->chapters as $chapterIndex => $chapter)
                <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
                    <!-- Chapter Header -->
                    <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 20px; color: #ffffff;" class="mobile-p-sm">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                            <div>
                                <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 8px 0;" class="mobile-text-sm">
                                    <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; margin-right: 12px;">
                                        Chương {{ $chapterIndex + 1 }}
                                    </span>
                                    {{ $chapter->title }}
                                </h3>
                                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">
                                    <i class="fas fa-play-circle" style="margin-right: 6px;"></i>
                                    {{ $chapter->lessons->count() }} Bài Học
                                </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                @php $chapterProgress = rand(0, 100); @endphp
                                <span style="background-color: rgba(255, 255, 255, 0.2); padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $chapterProgress }}%
                                </span>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div style="margin-top: 16px;">
                            <div style="width: 100%; height: 6px; background-color: rgba(255, 255, 255, 0.3); border-radius: 3px; overflow: hidden;">
                                <div style="width: {{ $chapterProgress }}%; height: 100%; background-color: #ffffff; border-radius: 3px; transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Lessons List -->
                    @if($chapter->lessons->count() > 0)
                        <div style="padding: 0;">
                            @foreach($chapter->lessons as $lessonIndex => $lesson)
                                <div style="border-bottom: 1px solid #f3f4f6; {{ $loop->last ? 'border-bottom: none;' : '' }}">
                                    @if($hasAccess)
                                        <a href="{{ route('user.subjects.lesson', [$subject, $chapter, $lesson]) }}"
                                           style="display: block; padding: 20px; text-decoration: none; transition: all 0.3s ease;"
                                           class="mobile-p-sm"
                                           onmouseover="this.style.backgroundColor='#f8fafc'"
                                           onmouseout="this.style.backgroundColor='transparent'">
                                    @else
                                        <div style="display: block; padding: 20px; opacity: 0.6; cursor: not-allowed; position: relative;"
                                             class="mobile-p-sm">
                                            @if(!$subject->isFree())
                                                <div style="position: absolute; top: 50%; right: 20px; transform: translateY(-50%); background-color: #fef3c7; color: #d97706; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                                    <i class="fas fa-lock" style="margin-right: 4px;"></i>
                                                    Cần Mua
                                                </div>
                                            @endif
                                    @endif
                                        <div style="display: flex; align-items: center; gap: 16px;" class="mobile-stack mobile-gap-sm">
                                            <!-- Lesson Number -->
                                            <div style="width: 40px; height: 40px; background-color: #f3f4f6; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; color: #374151; flex-shrink: 0;">
                                                {{ $lessonIndex + 1 }}
                                            </div>

                                            <!-- Lesson Info -->
                                            <div style="flex: 1; min-width: 0;">
                                                <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 4px 0;" class="mobile-text-sm">
                                                    {{ $lesson->title }}
                                                </h4>
                                                <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                                                    @if($lesson->hasVideo())
                                                        <span style="background-color: #fef3c7; color: #d97706; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                                            <i class="fab fa-youtube" style="margin-right: 4px;"></i>
                                                            Video
                                                        </span>
                                                    @endif
                                                    @if($lesson->hasFile())
                                                        <span style="background-color: #d1fae5; color: #059669; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                                            <i class="fas fa-file-download" style="margin-right: 4px;"></i>
                                                            Tài Liệu
                                                        </span>
                                                    @endif
                                                    @php $lessonCompleted = rand(0, 1); @endphp
                                                    @if($lessonCompleted)
                                                        <span style="background-color: #dcfce7; color: #16a34a; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                                            <i class="fas fa-check-circle" style="margin-right: 4px;"></i>
                                                            Hoàn Thành
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>

                                            <!-- Action Icon -->
                                            <div style="color: #2563eb; font-size: 1.25rem;" class="mobile-hidden">
                                                @if($hasAccess)
                                                    <i class="fas fa-play-circle"></i>
                                                @else
                                                    <i class="fas fa-lock"></i>
                                                @endif
                                            </div>
                                        </div>
                                    @if($hasAccess)
                                        </a>
                                    @else
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div style="padding: 40px 20px; text-align: center; color: #6b7280;">
                            <i class="fas fa-video" style="font-size: 32px; margin-bottom: 12px; display: block; opacity: 0.5;"></i>
                            <p style="margin: 0; font-size: 0.875rem;">Chương này chưa có bài học nào</p>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div style="text-align: center; padding: 80px 20px; background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
                <i class="fas fa-bookmark" style="font-size: 48px; color: #9ca3af;"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin: 0 0 12px 0;">
                Chưa Có Chương Nào
            </h3>
            <p style="color: #6b7280; margin: 0 0 32px 0; font-size: 1rem; max-width: 400px; margin-left: auto; margin-right: auto;">
                Môn học này chưa có chương nào. Hãy liên hệ với giảng viên để cập nhật nội dung.
            </p>
            <div style="display: flex; gap: 16px; justify-content: center; flex-wrap: wrap;">
                <a href="{{ route('user.subjects.index') }}"
                   style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'"
                   onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-arrow-left" style="margin-right: 8px;"></i>
                    Quay Lại
                </a>
                @if($subject->group_link)
                    <a href="{{ $subject->group_link }}"
                       target="_blank"
                       style="background-color: #0088cc; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.backgroundColor='#006699'"
                       onmouseout="this.style.backgroundColor='#0088cc'">
                        <i class="fab fa-telegram" style="margin-right: 8px;"></i>
                        Nhóm Telegram
                    </a>
                @endif
            </div>
        </div>
    @endif
</x-app-layout>