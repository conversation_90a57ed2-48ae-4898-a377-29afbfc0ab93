<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-graduation-cap" style="margin-right: 8px; color: #2563eb;"></i>
                Tất <PERSON>hó<PERSON> Họ<PERSON>
            </h2>
            <div style="display: flex; gap: 8px; align-items: center;">
                <span style="background-color: #dbeafe; color: #2563eb; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                    <i class="fas fa-book" style="margin-right: 4px;"></i>
                    {{ $subjects->total() }} Khóa Học
                </span>
            </div>
        </div>
    </x-slot>

    <!-- Mobile Responsive CSS -->
    <style>
        @media (max-width: 768px) {
            .mobile-grid { grid-template-columns: 1fr !important; }
            .mobile-text-sm { font-size: 0.875rem !important; }
            .mobile-p-sm { padding: 16px !important; }
            .mobile-gap-sm { gap: 12px !important; }
            .mobile-stack { flex-direction: column !important; align-items: flex-start !important; }
            .mobile-hidden { display: none !important; }
        }
    </style>

    <!-- Filters and Search -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px; margin-bottom: 24px;" class="mobile-p-sm">
        <form method="GET" action="{{ route('user.all-subjects.index') }}">
            <div style="display: grid; grid-template-columns: 1fr auto auto auto; gap: 16px; align-items: end;" class="mobile-grid mobile-gap-sm">
                <!-- Search -->
                <div>
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-search" style="margin-right: 8px; color: #2563eb;"></i>
                        Tìm Kiếm Khóa Học
                    </label>
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Nhập tên khóa học..."
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease;"
                           onfocus="this.style.borderColor='#2563eb'"
                           onblur="this.style.borderColor='#e5e7eb'">
                </div>

                <!-- Filter by Price -->
                <div>
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-filter" style="margin-right: 8px; color: #2563eb;"></i>
                        Lọc Theo Giá
                    </label>
                    <select name="filter" 
                            style="padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; background-color: #ffffff; min-width: 150px;">
                        <option value="">Tất Cả</option>
                        <option value="free" {{ request('filter') === 'free' ? 'selected' : '' }}>Miễn Phí</option>
                        <option value="paid" {{ request('filter') === 'paid' ? 'selected' : '' }}>Có Phí</option>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-sort" style="margin-right: 8px; color: #2563eb;"></i>
                        Sắp Xếp
                    </label>
                    <select name="sort" 
                            style="padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; background-color: #ffffff; min-width: 150px;">
                        <option value="title" {{ request('sort') === 'title' ? 'selected' : '' }}>Tên A-Z</option>
                        <option value="price" {{ request('sort') === 'price' ? 'selected' : '' }}>Giá Thấp-Cao</option>
                        <option value="lessons" {{ request('sort') === 'lessons' ? 'selected' : '' }}>Số Bài Học</option>
                        <option value="created" {{ request('sort') === 'created' ? 'selected' : '' }}>Mới Nhất</option>
                    </select>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; white-space: nowrap;"
                        onmouseover="this.style.backgroundColor='#1d4ed8'"
                        onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-search" style="margin-right: 8px;"></i>
                    Tìm Kiếm
                </button>
            </div>
        </form>
    </div>

    @if($subjects->count() > 0)
        <!-- Subjects Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 24px; margin-bottom: 32px;" class="mobile-grid mobile-gap-sm">
            @foreach($subjects as $subject)
                <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden; transition: all 0.3s ease; cursor: pointer;"
                     onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 25px rgba(0, 0, 0, 0.15)'"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)'"
                     onclick="window.location.href='{{ route('user.subjects.show', $subject) }}'">
                    
                    <!-- Subject Thumbnail -->
                    <div style="position: relative; height: 200px; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); overflow: hidden;">
                        @if($subject->thumbnail)
                            <img src="{{ Storage::url($subject->thumbnail) }}"
                                 alt="{{ $subject->title }}"
                                 style="width: 100%; height: 100%; object-fit: cover;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(37, 99, 235, 0.2) 0%, rgba(29, 78, 216, 0.2) 100%);"></div>
                        @else
                            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #ffffff;">
                                <i class="fas fa-graduation-cap" style="font-size: 64px; opacity: 0.7;"></i>
                            </div>
                        @endif
                        
                        <!-- Price Badge -->
                        <div style="position: absolute; top: 16px; left: 16px;">
                            @if($subject->isFree())
                                <span style="background-color: rgba(16, 163, 74, 0.9); color: #ffffff; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600; backdrop-filter: blur(10px);">
                                    <i class="fas fa-gift" style="margin-right: 4px;"></i>
                                    Miễn Phí
                                </span>
                            @else
                                <span style="background-color: rgba(217, 119, 6, 0.9); color: #ffffff; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600; backdrop-filter: blur(10px);">
                                    <i class="fas fa-tag" style="margin-right: 4px;"></i>
                                    {{ number_format($subject->price, 0, ',', '.') }}đ
                                </span>
                            @endif
                        </div>

                        <!-- Subject Stats -->
                        <div style="position: absolute; top: 16px; right: 16px; display: flex; gap: 8px;">
                            <span style="background-color: rgba(255, 255, 255, 0.2); color: #ffffff; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; backdrop-filter: blur(10px);">
                                <i class="fas fa-bookmark" style="margin-right: 4px;"></i>
                                {{ $subject->chapters_count }} Chương
                            </span>
                            <span style="background-color: rgba(255, 255, 255, 0.2); color: #ffffff; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; backdrop-filter: blur(10px);">
                                <i class="fas fa-play-circle" style="margin-right: 4px;"></i>
                                {{ $subject->lessons_count }} Bài
                            </span>
                        </div>
                    </div>

                    <!-- Subject Content -->
                    <div style="padding: 20px;" class="mobile-p-sm">
                        <h3 style="font-size: 1.25rem; font-weight: 700; color: #000000; margin: 0 0 8px 0; line-height: 1.3;" class="mobile-text-sm">
                            {{ $subject->title }}
                        </h3>
                        <p style="color: #6b7280; margin: 0 0 16px 0; font-size: 0.875rem; line-height: 1.5; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                            {{ $subject->description ?: 'Chưa có mô tả cho khóa học này.' }}
                        </p>

                        <!-- Instructor Info -->
                        @if($subject->instructor_name)
                            <div style="margin-bottom: 16px; padding: 8px 12px; background-color: #f8fafc; border-radius: 8px; border-left: 3px solid #7c3aed;">
                                <p style="margin: 0; color: #7c3aed; font-size: 0.875rem; font-weight: 600;">
                                    <i class="fas fa-chalkboard-teacher" style="margin-right: 6px;"></i>
                                    Giáo Viên : {{ $subject->instructor_name }}
                                </p>
                            </div>
                        @endif

                        <!-- Action Buttons -->
                        <div style="display: flex; gap: 12px; align-items: center;" class="mobile-stack mobile-gap-sm">
                            <a href="{{ route('user.subjects.show', $subject) }}" 
                               style="flex: 1; background-color: #2563eb; color: #ffffff; padding: 10px 16px; border-radius: 8px; text-decoration: none; font-weight: 600; text-align: center; font-size: 0.875rem; transition: all 0.3s ease;"
                               onmouseover="this.style.backgroundColor='#1d4ed8'"
                               onmouseout="this.style.backgroundColor='#2563eb'"
                               onclick="event.stopPropagation()">
                                <i class="fas fa-eye" style="margin-right: 6px;"></i>
                                Xem Chi Tiết
                            </a>
                            
                            @if($subject->group_link)
                                <a href="{{ $subject->group_link }}" 
                                   target="_blank"
                                   style="background-color: #0088cc; color: #ffffff; padding: 10px 12px; border-radius: 8px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#006699'"
                                   onmouseout="this.style.backgroundColor='#0088cc'"
                                   onclick="event.stopPropagation()"
                                   title="Tham Gia Nhóm Telegram">
                                    <i class="fab fa-telegram"></i>
                                </a>
                            @endif
                        </div>

                        <!-- Subject Meta -->
                        <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #f3f4f6; display: flex; justify-content: space-between; align-items: center; font-size: 0.75rem; color: #9ca3af;">
                            <span>
                                <i class="fas fa-calendar-alt" style="margin-right: 4px;"></i>
                                {{ $subject->created_at->format('d/m/Y') }}
                            </span>
                            <span class="mobile-hidden">
                                <i class="fas fa-clock" style="margin-right: 4px;"></i>
                                {{ $subject->updated_at->diffForHumans() }}
                            </span>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($subjects->hasPages())
            <div style="display: flex; justify-content: center;">
                <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 16px;">
                    {{ $subjects->appends(request()->query())->links() }}
                </div>
            </div>
        @endif
    @else
        <!-- Empty State -->
        <div style="text-align: center; padding: 80px 20px; background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
                <i class="fas fa-graduation-cap" style="font-size: 48px; color: #9ca3af;"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin: 0 0 12px 0;">
                Không Tìm Thấy Khóa Học Nào
            </h3>
            <p style="color: #6b7280; margin: 0 0 32px 0; font-size: 1rem; max-width: 400px; margin-left: auto; margin-right: auto;">
                Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc để tìm thấy khóa học phù hợp.
            </p>
            <a href="{{ route('user.all-subjects.index') }}" 
               style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#1d4ed8'"
               onmouseout="this.style.backgroundColor='#2563eb'">
                <i class="fas fa-refresh" style="margin-right: 8px;"></i>
                Xem Tất Cả Khóa Học
            </a>
        </div>
    @endif
</x-app-layout>
