<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subjects', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->default(0)->after('group_link')->comment('<PERSON>iá khóa học (VND)');
            $table->boolean('is_free')->default(true)->after('price')->comment('<PERSON>h<PERSON><PERSON> học miễn phí');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subjects', function (Blueprint $table) {
            $table->dropColumn(['price', 'is_free']);
        });
    }
};
