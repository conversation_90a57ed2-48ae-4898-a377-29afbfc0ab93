<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Hiển Thị <PERSON>h Sách <PERSON>ời Dùng
     */
    public function index()
    {
        $users = User::orderBy('created_at', 'desc')->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Hiển Thị Form Tạo Người Dùng Mới
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Lưu Ng<PERSON>ời Dùng <PERSON>ới
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'is_admin' => 'boolean',
        ]);

        $data = $request->only(['name', 'email']);
        $data['password'] = Hash::make($request->password);
        $data['is_admin'] = $request->has('is_admin');
        $data['email_verified_at'] = now(); // Tự động verify email

        User::create($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'Người Dùng Đã Được Tạo Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Người Dùng
     */
    public function show(User $user)
    {
        return view('admin.users.show', compact('user'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa Người Dùng
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Cập Nhật Người Dùng
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'is_admin' => 'boolean',
        ]);

        $data = $request->only(['name', 'email']);

        // Chỉ cập nhật password nếu có nhập
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $data['is_admin'] = $request->has('is_admin');

        $user->update($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'Người Dùng Đã Được Cập Nhật Thành Công!');
    }

    /**
     * Xóa Người Dùng
     */
    public function destroy(User $user)
    {
        // Không cho phép xóa chính mình
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Bạn Không Thể Xóa Chính Mình!');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Người Dùng Đã Được Xóa Thành Công!');
    }

    /**
     * Toggle Admin Status
     */
    public function toggleAdmin(User $user)
    {
        // Không cho phép thay đổi quyền của chính mình
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Bạn Không Thể Thay Đổi Quyền Của Chính Mình!');
        }

        $user->update(['is_admin' => !$user->is_admin]);

        $status = $user->is_admin ? 'Admin' : 'Người Dùng Thường';
        return redirect()->route('admin.users.index')
            ->with('success', "Đã Chuyển {$user->name} Thành {$status}!");
    }
}
