<x-guest-layout>
    <!-- Session Status -->
    @if (session('status'))
        <div style="margin-bottom: 16px; padding: 12px; background-color: #dcfce7; border: 1px solid #16a34a; border-radius: 8px; color: #15803d;">
            {{ session('status') }}
        </div>
    @endif

    <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 24px; text-align: center; color: #000000;">
        <PERSON><PERSON><PERSON>
    </h2>

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Email Address -->
        <div style="margin-bottom: 20px;">
            <label for="email" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000;">
                Email
            </label>
            <input id="email" type="email" name="email" value="{{ old('email') }}" required autofocus autocomplete="username"
                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                   placeholder="Nhập Email Của Bạn">
            @error('email')
                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">{{ $message }}</div>
            @enderror
        </div>

        <!-- Password -->
        <div style="margin-bottom: 20px;">
            <label for="password" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000;">
                Mật Khẩu
            </label>
            <input id="password" type="password" name="password" required autocomplete="current-password"
                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                   placeholder="Nhập Mật Khẩu">
            @error('password')
                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">{{ $message }}</div>
            @enderror
        </div>

        <!-- Remember Me -->
        <div style="margin-bottom: 24px;">
            <label for="remember_me" style="display: flex; align-items: center; cursor: pointer;">
                <input id="remember_me" type="checkbox" name="remember"
                       style="margin-right: 8px; width: 16px; height: 16px; accent-color: #2563eb;">
                <span style="font-size: 14px; color: #6b7280;">Ghi Nhớ Đăng Nhập</span>
            </label>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}"
                   style="font-size: 14px; color: #2563eb; text-decoration: none; transition: color 0.3s ease;"
                   onmouseover="this.style.color='#1d4ed8'"
                   onmouseout="this.style.color='#2563eb'">
                    Quên Mật Khẩu?
                </a>
            @endif

            <button type="submit" class="btn-primary"
                    style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease;"
                    onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(37, 99, 235, 0.3)'"
                    onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                Đăng Nhập
            </button>
        </div>

        @if (Route::has('register'))
            <div style="text-align: center; padding-top: 16px; border-top: 1px solid #e5e7eb;">
                <span style="color: #6b7280; font-size: 14px;">Chưa Có Tài Khoản? </span>
                <a href="{{ route('register') }}"
                   style="color: #2563eb; text-decoration: none; font-weight: 600; transition: color 0.3s ease;"
                   onmouseover="this.style.color='#1d4ed8'"
                   onmouseout="this.style.color='#2563eb'">
                    Đăng Ký Ngay
                </a>
            </div>
        @endif
    </form>
</x-guest-layout>
