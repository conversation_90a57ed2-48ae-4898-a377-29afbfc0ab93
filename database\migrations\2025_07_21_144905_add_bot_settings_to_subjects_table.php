<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subjects', function (Blueprint $table) {
            $table->string('telegram_group_id')->nullable()->after('group_link')->comment('Telegram Group Chat ID');
            $table->string('discord_webhook_url')->nullable()->after('telegram_group_id')->comment('Discord Webhook URL');
            $table->boolean('auto_notify')->default(true)->after('discord_webhook_url')->comment('Tự động thông báo bài học mới');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subjects', function (Blueprint $table) {
            $table->dropColumn(['telegram_group_id', 'discord_webhook_url', 'auto_notify']);
        });
    }
};
