<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    /**
     * Hiển thị trang cài đặt
     */
    public function index()
    {
        $settings = Setting::orderBy('group')->orderBy('order')->get()->groupBy('group');

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Cập nhật cài đặt
     */
    public function update(Request $request)
    {
        $request->validate([
            'settings' => 'required|array',
        ]);

        foreach ($request->settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();

            if ($setting) {
                // Xử lý upload file
                if ($setting->type === 'file' && $request->hasFile("files.{$key}")) {
                    $file = $request->file("files.{$key}");

                    // Xóa file cũ nếu có
                    if ($setting->value && Storage::disk('public')->exists($setting->value)) {
                        Storage::disk('public')->delete($setting->value);
                    }

                    // Upload file mới
                    $path = $file->store('settings', 'public');
                    $value = $path;
                }

                Setting::set($key, $value);
            }
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Cài đặt đã được cập nhật thành công!');
    }

    /**
     * Khởi tạo settings mặc định
     */
    public function initializeDefaults()
    {
        $defaultSettings = [
            // SEO Settings
            [
                'key' => 'site_title',
                'value' => 'PanDaEdu - Nền Tảng Học Tập Trực Tuyến',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'Tiêu Đề Website',
                'description' => 'Tiêu đề chính của website, hiển thị trên tab trình duyệt',
                'order' => 1
            ],
            [
                'key' => 'site_description',
                'value' => 'Nền tảng học tập trực tuyến hàng đầu với các khóa học chất lượng cao',
                'type' => 'textarea',
                'group' => 'seo',
                'label' => 'Mô Tả Website',
                'description' => 'Mô tả ngắn gọn về website, hiển thị trong kết quả tìm kiếm',
                'order' => 2
            ],
            [
                'key' => 'site_keywords',
                'value' => 'học tập trực tuyến, khóa học, giáo dục, PanDaEdu',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'Từ Khóa SEO',
                'description' => 'Các từ khóa chính của website, cách nhau bằng dấu phẩy',
                'order' => 3
            ],
            [
                'key' => 'site_author',
                'value' => 'PanDaEdu Team',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'Tác Giả',
                'description' => 'Tên tác giả hoặc tổ chức sở hữu website',
                'order' => 4
            ],

            // Media Settings
            [
                'key' => 'site_logo',
                'value' => null,
                'type' => 'file',
                'group' => 'media',
                'label' => 'Logo Website',
                'description' => 'Logo chính của website (khuyến nghị: 200x60px, PNG/JPG)',
                'order' => 1
            ],
            [
                'key' => 'site_favicon',
                'value' => null,
                'type' => 'file',
                'group' => 'media',
                'label' => 'Favicon',
                'description' => 'Icon nhỏ hiển thị trên tab trình duyệt (khuyến nghị: 32x32px, ICO/PNG)',
                'order' => 2
            ],
            [
                'key' => 'site_og_image',
                'value' => null,
                'type' => 'file',
                'group' => 'media',
                'label' => 'Thumbnail Chia Sẻ',
                'description' => 'Hình ảnh hiển thị khi chia sẻ website (khuyến nghị: 1200x630px)',
                'order' => 3
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'media',
                'label' => 'Chế Độ Bảo Trì',
                'description' => 'Bật/tắt chế độ bảo trì website',
                'order' => 4
            ],

            // Contact Settings
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'contact',
                'label' => 'Email Liên Hệ',
                'description' => 'Địa chỉ email chính để liên hệ',
                'order' => 1
            ],
            [
                'key' => 'contact_phone',
                'value' => '+84 123 456 789',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Số Điện Thoại',
                'description' => 'Số điện thoại liên hệ',
                'order' => 2
            ],
            [
                'key' => 'contact_address',
                'value' => 'Hà Nội, Việt Nam',
                'type' => 'textarea',
                'group' => 'contact',
                'label' => 'Địa Chỉ',
                'description' => 'Địa chỉ văn phòng hoặc trụ sở chính',
                'order' => 3
            ],

            // Bot Settings
            [
                'key' => 'telegram_bot_token',
                'value' => '',
                'type' => 'text',
                'group' => 'bot',
                'label' => 'Telegram Bot Token',
                'description' => 'Token Bot Telegram Từ @BotFather',
                'order' => 1
            ],
            [
                'key' => 'telegram_community_group',
                'value' => '',
                'type' => 'text',
                'group' => 'bot',
                'label' => 'Telegram Cộng Đồng Chung',
                'description' => 'Link Nhóm Telegram Cho Tất Cả Học Sinh',
                'order' => 2
            ],
            [
                'key' => 'discord_community_server',
                'value' => '',
                'type' => 'text',
                'group' => 'bot',
                'label' => 'Discord Server Cộng Đồng',
                'description' => 'Link Discord Server Cho Tất Cả Học Sinh',
                'order' => 3
            ],
            [
                'key' => 'bot_welcome_message',
                'value' => 'Chào Mừng Bạn Đến Với Cộng Đồng Học Tập!',
                'type' => 'textarea',
                'group' => 'bot',
                'label' => 'Tin Nhắn Chào Mừng Bot',
                'description' => 'Tin Nhắn Bot Gửi Khi Có Thành Viên Mới',
                'order' => 4
            ],
            [
                'key' => 'bot_help_message',
                'value' => 'Liên Hệ Admin Để Được Hỗ Trợ Tốt Nhất!',
                'type' => 'textarea',
                'group' => 'bot',
                'label' => 'Tin Nhắn Hỗ Trợ Bot',
                'description' => 'Tin Nhắn Bot Gửi Khi Được Yêu Cầu Hỗ Trợ',
                'order' => 5
            ],

            // Advanced Settings
            [
                'key' => 'header_scripts',
                'value' => '',
                'type' => 'textarea',
                'group' => 'advanced',
                'label' => 'Script Header',
                'description' => 'Mã JavaScript/CSS Tùy Chỉnh Trong Thẻ <head>',
                'order' => 1
            ],
            [
                'key' => 'footer_scripts',
                'value' => '',
                'type' => 'textarea',
                'group' => 'advanced',
                'label' => 'Script Footer',
                'description' => 'Mã JavaScript Tùy Chỉnh Trước Thẻ </body>',
                'order' => 2
            ],
        ];

        foreach ($defaultSettings as $settingData) {
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                $settingData
            );
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Đã khởi tạo cài đặt mặc định thành công!');
    }
}
