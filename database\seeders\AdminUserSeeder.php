<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo Admin User
        User::create([
            'name' => 'Admin PanDa Edu',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
        ]);

        // Tạo User Thường Để Test
        User::create([
            'name' => 'Học Viên Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('user123'),
            'role' => 'user',
        ]);
    }
}
