<?php

use App\Models\Setting;
use Illuminate\Support\Facades\Storage;

if (!function_exists('setting')) {
    /**
     * Get setting value by key
     */
    function setting($key, $default = null)
    {
        return Setting::get($key, $default);
    }
}

if (!function_exists('setting_file_url')) {
    /**
     * Get setting file URL
     */
    function setting_file_url($key, $default = null)
    {
        $value = Setting::get($key);
        
        if ($value && Storage::disk('public')->exists($value)) {
            return Storage::url($value);
        }
        
        return $default;
    }
}

if (!function_exists('site_logo')) {
    /**
     * Get site logo URL
     */
    function site_logo($default = null)
    {
        return setting_file_url('site_logo', $default);
    }
}

if (!function_exists('site_favicon')) {
    /**
     * Get site favicon URL
     */
    function site_favicon($default = null)
    {
        return setting_file_url('site_favicon', $default ?: '/favicon.ico');
    }
}

if (!function_exists('site_title')) {
    /**
     * Get site title
     */
    function site_title($default = null)
    {
        return setting('site_title', $default ?: config('app.name'));
    }
}

if (!function_exists('site_description')) {
    /**
     * Get site description
     */
    function site_description($default = null)
    {
        return setting('site_description', $default);
    }
}

if (!function_exists('site_keywords')) {
    /**
     * Get site keywords
     */
    function site_keywords($default = null)
    {
        return setting('site_keywords', $default);
    }
}

if (!function_exists('site_author')) {
    /**
     * Get site author
     */
    function site_author($default = null)
    {
        return setting('site_author', $default);
    }
}

if (!function_exists('site_og_image')) {
    /**
     * Get site Open Graph image URL
     */
    function site_og_image($default = null)
    {
        return setting_file_url('site_og_image', $default);
    }
}

if (!function_exists('contact_email')) {
    /**
     * Get contact email
     */
    function contact_email($default = null)
    {
        return setting('contact_email', $default);
    }
}

if (!function_exists('contact_phone')) {
    /**
     * Get contact phone
     */
    function contact_phone($default = null)
    {
        return setting('contact_phone', $default);
    }
}

if (!function_exists('contact_address')) {
    /**
     * Get contact address
     */
    function contact_address($default = null)
    {
        return setting('contact_address', $default);
    }
}

if (!function_exists('header_scripts')) {
    /**
     * Get header scripts
     */
    function header_scripts()
    {
        return setting('header_scripts', '');
    }
}

if (!function_exists('footer_scripts')) {
    /**
     * Get footer scripts
     */
    function footer_scripts()
    {
        return setting('footer_scripts', '');
    }
}

if (!function_exists('maintenance_mode')) {
    /**
     * Check If Maintenance Mode Is Enabled
     */
    function maintenance_mode()
    {
        return (bool) setting('maintenance_mode', false);
    }
}

if (!function_exists('telegram_bot_token')) {
    /**
     * Get Telegram Bot Token
     */
    function telegram_bot_token($default = null)
    {
        return setting('telegram_bot_token', $default);
    }
}

if (!function_exists('telegram_community_group')) {
    /**
     * Get Telegram Community Group Link
     */
    function telegram_community_group($default = null)
    {
        return setting('telegram_community_group', $default);
    }
}

if (!function_exists('discord_community_server')) {
    /**
     * Get Discord Community Server Link
     */
    function discord_community_server($default = null)
    {
        return setting('discord_community_server', $default);
    }
}

if (!function_exists('bot_welcome_message')) {
    /**
     * Get Bot Welcome Message
     */
    function bot_welcome_message($default = null)
    {
        return setting('bot_welcome_message', $default ?: 'Chào Mừng Bạn Đến Với Cộng Đồng Học Tập!');
    }
}

if (!function_exists('bot_help_message')) {
    /**
     * Get Bot Help Message
     */
    function bot_help_message($default = null)
    {
        return setting('bot_help_message', $default ?: 'Liên Hệ Admin Để Được Hỗ Trợ Tốt Nhất!');
    }
}
