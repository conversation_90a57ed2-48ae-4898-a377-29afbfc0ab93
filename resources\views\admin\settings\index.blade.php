<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-cogs" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON>i Đặt Hệ T<PERSON>ống
            </h2>
            <a href="{{ route('admin.settings.initialize') }}"
               style="background-color: #059669; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#047857'"
               onmouseout="this.style.backgroundColor='#059669'"
               onclick="return confirm('Bạn có chắc chắn muốn khởi tạo lại cài đặt mặc định?')">
                <i class="fas fa-sync-alt" style="margin-right: 8px;"></i>
                Khởi Tạo Mặc Định
            </a>
        </div>
    </x-slot>

    @if(session('success'))
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; color: #15803d; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            {{ session('success') }}
        </div>
    @endif

    <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <!-- Settings Tabs -->
        <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
            <!-- Tab Navigation -->
            <div style="background-color: #f8fafc; border-bottom: 1px solid #e5e7eb; padding: 0;">
                <div style="display: flex; overflow-x: auto;" class="mobile-scroll">
                    <button type="button" onclick="showTab('seo')" id="tab-seo" 
                            style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #6b7280; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap;"
                            class="tab-button active">
                        <i class="fas fa-search" style="margin-right: 8px;"></i>
                        Cấu Hình SEO
                    </button>
                    <button type="button" onclick="showTab('media')" id="tab-media"
                            style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #6b7280; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap;"
                            class="tab-button">
                        <i class="fas fa-images" style="margin-right: 8px;"></i>
                        Hình Ảnh & Media
                    </button>
                    <button type="button" onclick="showTab('contact')" id="tab-contact"
                            style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #6b7280; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap;"
                            class="tab-button">
                        <i class="fas fa-address-book" style="margin-right: 8px;"></i>
                        Thông Tin Liên Hệ
                    </button>
                    <button type="button" onclick="showTab('bot')" id="tab-bot"
                            style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #6b7280; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap;"
                            class="tab-button">
                        <i class="fas fa-robot" style="margin-right: 8px;"></i>
                        Cấu Hình Bot
                    </button>
                    <button type="button" onclick="showTab('advanced')" id="tab-advanced"
                            style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #6b7280; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap;"
                            class="tab-button">
                        <i class="fas fa-code" style="margin-right: 8px;"></i>
                        Cài Đặt Nâng Cao
                    </button>
                </div>
            </div>

            <!-- Tab Content -->
            <div style="padding: 32px;" class="mobile-p-sm">
                @foreach(['seo', 'media', 'contact', 'bot', 'advanced'] as $group)
                    <div id="content-{{ $group }}" class="tab-content" style="{{ $group === 'seo' ? '' : 'display: none;' }}">
                        @if($group === 'seo')
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                                <i class="fas fa-search" style="margin-right: 8px; color: #2563eb;"></i>
                                Cấu Hình SEO
                            </h3>
                            <p style="color: #6b7280; margin: 0 0 32px 0;">
                                Cấu hình các thông tin SEO cơ bản để tối ưu hóa website trên các công cụ tìm kiếm.
                            </p>
                        @elseif($group === 'media')
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                                <i class="fas fa-images" style="margin-right: 8px; color: #2563eb;"></i>
                                Hình Ảnh & Media
                            </h3>
                            <p style="color: #6b7280; margin: 0 0 32px 0;">
                                Quản lý logo, favicon và các hình ảnh đại diện cho website.
                            </p>
                        @elseif($group === 'contact')
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                                <i class="fas fa-address-book" style="margin-right: 8px; color: #2563eb;"></i>
                                Thông Tin Liên Hệ
                            </h3>
                            <p style="color: #6b7280; margin: 0 0 32px 0;">
                                Cập Nhật Thông Tin Liên Hệ Hiển Thị Trên Website.
                            </p>
                        @elseif($group === 'bot')
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                                <i class="fas fa-robot" style="margin-right: 8px; color: #2563eb;"></i>
                                Cấu Hình Bot Telegram & Discord
                            </h3>
                            <p style="color: #6b7280; margin: 0 0 32px 0;">
                                Cấu Hình Bot Token, Link Cộng Đồng Và Tin Nhắn Tự Động.
                            </p>
                        @else
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                                <i class="fas fa-code" style="margin-right: 8px; color: #2563eb;"></i>
                                Cài Đặt Nâng Cao
                            </h3>
                            <p style="color: #6b7280; margin: 0 0 32px 0;">
                                Thêm mã JavaScript/CSS tùy chỉnh vào website.
                            </p>
                        @endif

                        @if(isset($settings[$group]))
                            <div style="display: grid; gap: 24px;">
                                @foreach($settings[$group] as $setting)
                                    <div>
                                        <label for="{{ $setting->key }}" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                            {{ $setting->label }}
                                            @if(in_array($setting->key, ['site_title', 'site_description', 'contact_email']))
                                                <span style="color: #dc2626;">*</span>
                                            @endif
                                        </label>

                                        @if($setting->type === 'text' || $setting->type === 'email')
                                            <input type="{{ $setting->type }}"
                                                   id="{{ $setting->key }}"
                                                   name="settings[{{ $setting->key }}]"
                                                   value="{{ old("settings.{$setting->key}", $setting->value) }}"
                                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">

                                        @elseif($setting->type === 'textarea')
                                            <textarea id="{{ $setting->key }}"
                                                      name="settings[{{ $setting->key }}]"
                                                      rows="{{ $setting->key === 'site_description' ? '3' : '6' }}"
                                                      style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff; resize: vertical;"
                                                      onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                                      onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">{{ old("settings.{$setting->key}", $setting->value) }}</textarea>

                                        @elseif($setting->type === 'file')
                                            <div style="border: 2px dashed #e5e7eb; border-radius: 8px; padding: 24px; text-align: center;">
                                                @if($setting->value && Storage::disk('public')->exists($setting->value))
                                                    <div style="margin-bottom: 16px;">
                                                        <img src="{{ Storage::url($setting->value) }}" 
                                                             alt="{{ $setting->label }}"
                                                             style="max-width: 200px; max-height: 100px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                                    </div>
                                                @endif
                                                <input type="file"
                                                       id="{{ $setting->key }}"
                                                       name="files[{{ $setting->key }}]"
                                                       accept="image/*"
                                                       style="margin-bottom: 8px;">
                                                <input type="hidden" name="settings[{{ $setting->key }}]" value="{{ $setting->value }}">
                                            </div>

                                        @elseif($setting->type === 'boolean')
                                            <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                                <input type="hidden" name="settings[{{ $setting->key }}]" value="0">
                                                <input type="checkbox"
                                                       id="{{ $setting->key }}"
                                                       name="settings[{{ $setting->key }}]"
                                                       value="1"
                                                       {{ old("settings.{$setting->key}", $setting->value) ? 'checked' : '' }}
                                                       style="width: 20px; height: 20px; accent-color: #2563eb;">
                                                <span style="font-weight: 500; color: #374151;">
                                                    {{ $setting->value ? 'Đang bật' : 'Đang tắt' }}
                                                </span>
                                            </label>
                                        @endif

                                        @if($setting->description)
                                            <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                                {{ $setting->description }}
                                            </div>
                                        @endif

                                        @error("settings.{$setting->key}")
                                            <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                                <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div style="text-align: center; padding: 40px; color: #6b7280;">
                                <i class="fas fa-cog" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                                <p style="margin: 0;">Chưa có cài đặt nào trong nhóm này.</p>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>

            <!-- Submit Button -->
            <div style="background-color: #f8fafc; border-top: 1px solid #e5e7eb; padding: 24px; text-align: right;">
                <button type="submit"
                        style="background-color: #2563eb; color: #ffffff; padding: 12px 32px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 1rem;"
                        onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'"
                        onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                    <i class="fas fa-save" style="margin-right: 8px;"></i>
                    Lưu Cài Đặt
                </button>
            </div>
        </div>
    </form>

    <!-- JavaScript for Tabs -->
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.style.color = '#6b7280';
                button.style.borderBottomColor = 'transparent';
            });
            
            // Show selected tab content
            document.getElementById('content-' + tabName).style.display = 'block';
            
            // Add active class to selected tab button
            const activeButton = document.getElementById('tab-' + tabName);
            activeButton.style.color = '#2563eb';
            activeButton.style.borderBottomColor = '#2563eb';
        }
    </script>
</x-admin-layout>
