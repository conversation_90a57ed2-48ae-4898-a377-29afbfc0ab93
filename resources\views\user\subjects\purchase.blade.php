@extends('layouts.app')

@section('title', '<PERSON><PERSON>: ' . $subject->title)

@section('content')
<div style="max-width: 800px; margin: 0 auto; padding: 20px;">
    <!-- Header -->
    <div style="text-align: center; margin-bottom: 40px;">
        <h1 style="color: #2d3748; margin-bottom: 10px; font-size: 2.5rem;">🛒 <PERSON><PERSON></h1>
        <p style="color: #718096; font-size: 1.1rem;"><PERSON><PERSON><PERSON><PERSON>p <PERSON></p>
    </div>

    <!-- Subject Info Card -->
    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 30px; margin-bottom: 30px;">
        <div style="display: flex; gap: 20px; align-items: flex-start;" class="mobile-stack">
            @if($subject->thumbnail)
                <img src="{{ asset('storage/' . $subject->thumbnail) }}" 
                     alt="{{ $subject->title }}"
                     style="width: 150px; height: 100px; object-fit: cover; border-radius: 8px; flex-shrink: 0;" 
                     class="mobile-w-full">
            @endif
            
            <div style="flex: 1;">
                <h2 style="color: #2d3748; margin-bottom: 15px; font-size: 1.8rem;">{{ $subject->title }}</h2>
                
                @if($subject->description)
                    <p style="color: #4a5568; margin-bottom: 15px; line-height: 1.6;">{{ $subject->description }}</p>
                @endif

                <div style="display: flex; gap: 20px; margin-bottom: 15px;" class="mobile-stack mobile-gap-sm">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="color: #3182ce;">📚</span>
                        <span style="color: #4a5568;">{{ $subject->total_chapters }} Chương</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="color: #3182ce;">🎥</span>
                        <span style="color: #4a5568;">{{ $subject->total_lessons }} Bài Học</span>
                    </div>
                    @if($subject->instructor_name)
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="color: #3182ce;">👨‍🏫</span>
                            <span style="color: #4a5568;">{{ $subject->instructor_name }}</span>
                        </div>
                    @endif
                </div>

                <div style="background: #f7fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #3182ce;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: #2d3748; font-weight: 600; font-size: 1.1rem;">Giá Môn Học:</span>
                        <span style="color: #e53e3e; font-weight: bold; font-size: 1.5rem;">{{ $subject->formatted_price }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Balance Info -->
    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 30px; margin-bottom: 30px;">
        <h3 style="color: #2d3748; margin-bottom: 20px; font-size: 1.5rem;">💰 Thông Tin Tài Khoản</h3>
        
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: #f7fafc; border-radius: 8px; margin-bottom: 15px;">
            <span style="color: #2d3748; font-weight: 600;">Số Dư Hiện Tại:</span>
            <span style="color: #38a169; font-weight: bold; font-size: 1.2rem;">{{ number_format($user->balance, 0, ',', '.') }} VND</span>
        </div>

        @if($user->balance < $subject->price)
            <div style="background: #fed7d7; border: 1px solid #fc8181; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="color: #c53030; font-size: 1.2rem;">⚠️</span>
                    <div>
                        <p style="color: #c53030; font-weight: 600; margin: 0;">Số Dư Không Đủ</p>
                        <p style="color: #c53030; margin: 5px 0 0 0;">
                            Bạn Cần Thêm {{ number_format($subject->price - $user->balance, 0, ',', '.') }} VND Để Mua Môn Học Này
                        </p>
                    </div>
                </div>
            </div>
        @else
            <div style="background: #c6f6d5; border: 1px solid #68d391; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="color: #38a169; font-size: 1.2rem;">✅</span>
                    <div>
                        <p style="color: #38a169; font-weight: 600; margin: 0;">Số Dư Đủ Để Mua</p>
                        <p style="color: #38a169; margin: 5px 0 0 0;">
                            Sau Khi Mua, Số Dư Còn Lại: {{ number_format($user->balance - $subject->price, 0, ',', '.') }} VND
                        </p>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Purchase Form -->
    @if($user->balance >= $subject->price)
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 30px; margin-bottom: 30px;">
            <h3 style="color: #2d3748; margin-bottom: 20px; font-size: 1.5rem;">🔒 Xác Nhận Mua Hàng</h3>
            
            <form method="POST" action="{{ route('user.subjects.purchase.balance', $subject) }}">
                @csrf
                
                <div style="background: #f7fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                        <input type="checkbox" name="confirm_purchase" value="1" required 
                               style="width: 18px; height: 18px; accent-color: #3182ce;">
                        <span style="color: #2d3748; font-weight: 600;">
                            Tôi Xác Nhận Mua Môn Học "{{ $subject->title }}" Với Giá {{ $subject->formatted_price }}
                        </span>
                    </label>
                </div>

                <div style="display: flex; gap: 15px; justify-content: center;" class="mobile-stack">
                    <a href="{{ route('user.subjects.show', $subject) }}" 
                       style="background: #e2e8f0; color: #4a5568; padding: 12px 30px; border-radius: 8px; text-decoration: none; font-weight: 600; text-align: center; flex: 1; max-width: 200px;">
                        ← Quay Lại
                    </a>
                    
                    <button type="submit" 
                            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; border: none; border-radius: 8px; font-weight: 600; cursor: pointer; flex: 1; max-width: 200px;">
                        💳 Mua Ngay
                    </button>
                </div>
            </form>
        </div>
    @else
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 30px; text-align: center;">
            <h3 style="color: #c53030; margin-bottom: 20px; font-size: 1.5rem;">💳 Nạp Thêm Tiền</h3>
            <p style="color: #4a5568; margin-bottom: 20px;">Bạn Cần Nạp Thêm Tiền Để Mua Môn Học Này</p>
            
            <div style="display: flex; gap: 15px; justify-content: center;" class="mobile-stack">
                <a href="{{ route('user.subjects.show', $subject) }}" 
                   style="background: #e2e8f0; color: #4a5568; padding: 12px 30px; border-radius: 8px; text-decoration: none; font-weight: 600; text-align: center; flex: 1; max-width: 200px;">
                    ← Quay Lại
                </a>
                
                <a href="#" 
                   style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; padding: 12px 30px; border-radius: 8px; text-decoration: none; font-weight: 600; text-align: center; flex: 1; max-width: 200px;">
                    💰 Nạp Tiền
                </a>
            </div>
        </div>
    @endif
</div>

<style>
@media (max-width: 768px) {
    .mobile-stack {
        flex-direction: column !important;
    }
    .mobile-gap-sm {
        gap: 10px !important;
    }
    .mobile-w-full {
        width: 100% !important;
    }
    .mobile-p-sm {
        padding: 15px !important;
    }
}
</style>
@endsection
