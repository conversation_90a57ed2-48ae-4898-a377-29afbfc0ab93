<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>@yield('title', site_title())</title>

        <!-- SEO Meta Tags -->
        <meta name="description" content="{{ site_description() }}">
        <meta name="keywords" content="{{ site_keywords() }}">
        <meta name="author" content="{{ site_author() }}">

        <!-- Open Graph Meta Tags -->
        <meta property="og:title" content="{{ site_title() }}">
        <meta property="og:description" content="{{ site_description() }}">
        <meta property="og:type" content="website">
        <meta property="og:url" content="{{ url()->current() }}">
        @if(site_og_image())
            <meta property="og:image" content="{{ site_og_image() }}">
        @endif

        <!-- Twitter Card Meta Tags -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="{{ site_title() }}">
        <meta name="twitter:description" content="{{ site_description() }}">
        @if(site_og_image())
            <meta name="twitter:image" content="{{ site_og_image() }}">
        @endif

        <!-- Favicon -->
        @if(site_favicon())
            <link rel="icon" type="image/x-icon" href="{{ site_favicon() }}">
            <link rel="shortcut icon" type="image/x-icon" href="{{ site_favicon() }}">
        @endif

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Custom Header Scripts -->
        @if(header_scripts())
            {!! header_scripts() !!}
        @endif
    </head>
    <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc;">
        <div style="display: flex; min-height: 100vh;">
            <!-- Sidebar -->
            <div style="width: 280px; background-color: #ffffff; border-right: 1px solid #e5e7eb; box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1); position: fixed; height: 100vh; overflow-y: auto;">
                <!-- Logo -->
                <div style="padding: 24px 20px; border-bottom: 1px solid #e5e7eb;">
                    @if(site_logo())
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                            <img src="{{ site_logo() }}"
                                 alt="{{ site_title() }}"
                                 style="height: 40px; width: auto; object-fit: contain;">
                            <div>
                                <h1 style="font-size: 1.25rem; font-weight: bold; color: #2563eb; margin: 0;">
                                    {{ site_title() }}
                                </h1>
                            </div>
                        </div>
                    @else
                        <h1 style="font-size: 1.5rem; font-weight: bold; color: #2563eb; margin: 0 0 8px 0;">
                            {{ site_title() }}
                        </h1>
                    @endif
                    <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">
                        {{ site_description() ?: 'Nền Tảng Học Online' }}
                    </p>
                </div>

                <!-- User Info -->
                <div style="padding: 20px; border-bottom: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background-color: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                            {{ substr(auth()->user()->name, 0, 1) }}
                        </div>
                        <div>
                            <p style="font-weight: 600; color: #000000; margin: 0; font-size: 0.875rem;">
                                {{ auth()->user()->name }}
                            </p>
                            <p style="font-size: 0.75rem; color: #6b7280; margin: 0;">
                                {{ auth()->user()->role === 'admin' ? 'Quản Trị Viên' : 'Học Viên' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav style="padding: 20px 0;">
                    <ul style="list-style: none; margin: 0; padding: 0;">
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('dashboard') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #374151; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('dashboard') ? 'background-color: #dbeafe; color: #2563eb; border-right: 3px solid #2563eb;' : '' }}"
                               onmouseover="if (!this.style.backgroundColor.includes('dbeafe')) { this.style.backgroundColor='#f3f4f6'; }"
                               onmouseout="if (!this.style.backgroundColor.includes('dbeafe')) { this.style.backgroundColor='transparent'; }">
                                <i class="fas fa-home" style="width: 16px; text-align: center;"></i>
                                <span style="font-weight: 500;">Trang Chủ</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('user.all-subjects.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #374151; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('user.all-subjects.*') ? 'background-color: #f3f4f6; color: #2563eb; border-right: 3px solid #2563eb;' : '' }}"
                               onmouseover="if (!this.style.backgroundColor.includes('f3f4f6')) { this.style.backgroundColor='#f3f4f6'; }"
                               onmouseout="if (!this.style.backgroundColor.includes('f3f4f6') || !this.style.borderRight.includes('2563eb')) { this.style.backgroundColor='transparent'; }">
                                <i class="fas fa-graduation-cap" style="width: 16px; text-align: center;"></i>
                                <span style="font-weight: 500;">Tất Cả Khóa Học</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('user.subjects.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #374151; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('user.subjects.*') ? 'background-color: #f3f4f6; color: #2563eb; border-right: 3px solid #2563eb;' : '' }}"
                               onmouseover="if (!this.style.backgroundColor.includes('f3f4f6')) { this.style.backgroundColor='#f3f4f6'; }"
                               onmouseout="if (!this.style.backgroundColor.includes('f3f4f6') || !this.style.borderRight.includes('2563eb')) { this.style.backgroundColor='transparent'; }">
                                <i class="fas fa-book" style="width: 16px; text-align: center;"></i>
                                <span style="font-weight: 500;">Môn Học Của Tôi</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('user.progress.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #374151; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('user.progress.*') ? 'background-color: #f3f4f6; color: #2563eb; border-right: 3px solid #2563eb;' : '' }}"
                               onmouseover="if (!this.style.backgroundColor.includes('f3f4f6')) { this.style.backgroundColor='#f3f4f6'; }"
                               onmouseout="if (!this.style.backgroundColor.includes('f3f4f6') || !this.style.borderRight.includes('2563eb')) { this.style.backgroundColor='transparent'; }">
                                <i class="fas fa-chart-line" style="width: 16px; text-align: center;"></i>
                                <span style="font-weight: 500;">Tiến Độ Học Tập</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('user.documents.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #374151; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('user.documents.*') ? 'background-color: #f3f4f6; color: #2563eb; border-right: 3px solid #2563eb;' : '' }}"
                               onmouseover="if (!this.style.backgroundColor.includes('f3f4f6')) { this.style.backgroundColor='#f3f4f6'; }"
                               onmouseout="if (!this.style.backgroundColor.includes('f3f4f6') || !this.style.borderRight.includes('2563eb')) { this.style.backgroundColor='transparent'; }">
                                <i class="fas fa-file-alt" style="width: 16px; text-align: center;"></i>
                                <span style="font-weight: 500;">Tài Liệu Học Tập</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('user.bots.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #374151; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('user.bots.*') ? 'background-color: #f3f4f6; color: #2563eb; border-right: 3px solid #2563eb;' : '' }}"
                               onmouseover="if (!this.style.backgroundColor.includes('f3f4f6')) { this.style.backgroundColor='#f3f4f6'; }"
                               onmouseout="if (!this.style.backgroundColor.includes('f3f4f6') || !this.style.borderRight.includes('2563eb')) { this.style.backgroundColor='transparent'; }">
                                <i class="fas fa-robot" style="width: 16px; text-align: center;"></i>
                                <span style="font-weight: 500;">Bot Telegram & Discord</span>
                            </a>
                        </li>

                        @if(auth()->user()->isAdmin())
                        <li style="margin: 20px 0 4px 0; padding: 0 20px;">
                            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 0;">
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('admin.dashboard') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #dc2626; text-decoration: none; transition: all 0.3s ease; font-weight: 600;"
                               onmouseover="this.style.backgroundColor='#fef2f2';"
                               onmouseout="this.style.backgroundColor='transparent';">
                                <i class="fas fa-user-cog" style="width: 16px; text-align: center;"></i>
                                <span>Khu Vực Quản Trị Viên</span>
                            </a>
                        </li>
                        @endif

                        <li style="margin: 20px 0 4px 0; padding: 0 20px;">
                            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 0;">
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('profile.edit') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #374151; text-decoration: none; transition: all 0.3s ease;"
                               onmouseover="this.style.backgroundColor='#f3f4f6';"
                               onmouseout="this.style.backgroundColor='transparent';">
                                <i class="fas fa-cog" style="width: 16px; text-align: center;"></i>
                                <span style="font-weight: 500;">Cài Đặt Thông Tin</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <form method="POST" action="{{ route('logout') }}" style="margin: 0;">
                                @csrf
                                <button type="submit"
                                        style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #dc2626; background: none; border: none; width: 100%; text-align: left; cursor: pointer; transition: all 0.3s ease; font-family: inherit; font-size: inherit;"
                                        onmouseover="this.style.backgroundColor='#fef2f2';"
                                        onmouseout="this.style.backgroundColor='transparent';">
                                    <i class="fas fa-sign-out-alt" style="width: 16px; text-align: center;"></i>
                                    <span style="font-weight: 500;">Đăng Xuất</span>
                                </button>
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- Main Content -->
            <div style="margin-left: 280px; flex: 1; display: flex; flex-direction: column;">
                <!-- Top Header -->
                @isset($header)
                <header style="background-color: #ffffff; border-bottom: 1px solid #e5e7eb; padding: 16px 32px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                    {{ $header }}
                </header>
                @endisset

                <!-- Page Content -->
                <main style="flex: 1; padding: 32px;">
                    @yield('content')
                </main>
            </div>
        </div>

        <!-- Custom Footer Scripts -->
        @if(footer_scripts())
            {!! footer_scripts() !!}
        @endif
    </body>
</html>
