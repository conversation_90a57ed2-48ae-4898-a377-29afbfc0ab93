<?php

namespace App\Services;

use App\Models\Lesson;
use App\Models\Subject;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DiscordBotService
{
    /**
     * G<PERSON>i tin nhắn đến Discord qua Webhook
     */
    public function sendWebhookMessage($webhookUrl, $content, $embeds = null)
    {
        try {
            $payload = [
                'content' => $content,
                'username' => site_title() . ' Bot',
                'avatar_url' => site_logo() ?: null,
            ];

            if ($embeds) {
                $payload['embeds'] = $embeds;
            }

            $response = Http::post($webhookUrl, $payload);

            if ($response->successful()) {
                Log::info('Discord webhook message sent successfully', [
                    'webhook' => substr($webhookUrl, -20),
                    'content' => substr($content, 0, 100) . '...'
                ]);
                return true;
            } else {
                Log::error('Failed to send Discord webhook message', [
                    'webhook' => substr($webhookUrl, -20),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Discord webhook error', [
                'error' => $e->getMessage(),
                'webhook' => substr($webhookUrl, -20)
            ]);
            return false;
        }
    }

    /**
     * Thông báo bài học mới
     */
    public function notifyNewLesson(Lesson $lesson)
    {
        $subject = $lesson->chapter->subject;
        
        if (!$subject->discord_webhook_url || !$subject->auto_notify) {
            return false;
        }

        $embed = $this->createNewLessonEmbed($lesson);
        $content = "🎓 **Bài học mới đã được đăng!**";
        
        return $this->sendWebhookMessage($subject->discord_webhook_url, $content, [$embed]);
    }

    /**
     * Tạo embed cho bài học mới
     */
    private function createNewLessonEmbed(Lesson $lesson)
    {
        $subject = $lesson->chapter->subject;
        $chapter = $lesson->chapter;
        
        $embed = [
            'title' => $lesson->title,
            'description' => "Bài học mới trong môn **{$subject->title}**",
            'color' => 0x2563eb, // Blue color
            'fields' => [
                [
                    'name' => '📚 Môn học',
                    'value' => $subject->title,
                    'inline' => true
                ],
                [
                    'name' => '📖 Chương',
                    'value' => $chapter->title,
                    'inline' => true
                ],
                [
                    'name' => '👨‍🏫 Giáo viên',
                    'value' => $subject->instructor_name ?: 'Chưa cập nhật',
                    'inline' => true
                ]
            ],
            'footer' => [
                'text' => site_title(),
                'icon_url' => site_logo() ?: null
            ],
            'timestamp' => $lesson->created_at->toISOString(),
            'url' => route('user.subjects.lesson', [$subject, $chapter, $lesson])
        ];

        // Thêm thông tin video và tài liệu
        $features = [];
        if ($lesson->hasVideo()) {
            $features[] = '🎥 Video';
        }
        if ($lesson->hasDocument()) {
            $features[] = '📄 Tài liệu';
        }

        if (!empty($features)) {
            $embed['fields'][] = [
                'name' => '📋 Nội dung',
                'value' => implode(' • ', $features),
                'inline' => false
            ];
        }

        // Thêm thumbnail nếu có
        if ($subject->thumbnail) {
            $embed['thumbnail'] = [
                'url' => asset('storage/' . $subject->thumbnail)
            ];
        }

        return $embed;
    }

    /**
     * Gửi thông báo hệ thống
     */
    public function sendSystemNotification($webhookUrl, $title, $message, $type = 'info')
    {
        $colors = [
            'info' => 0x3b82f6,    // Blue
            'success' => 0x10b981, // Green
            'warning' => 0xf59e0b, // Yellow
            'error' => 0xef4444,   // Red
        ];

        $icons = [
            'info' => 'ℹ️',
            'success' => '✅',
            'warning' => '⚠️',
            'error' => '❌',
        ];

        $embed = [
            'title' => $icons[$type] . ' ' . $title,
            'description' => $message,
            'color' => $colors[$type] ?? $colors['info'],
            'footer' => [
                'text' => site_title() . ' - Thông báo hệ thống',
                'icon_url' => site_logo() ?: null
            ],
            'timestamp' => now()->toISOString()
        ];

        return $this->sendWebhookMessage($webhookUrl, '', [$embed]);
    }

    /**
     * Gửi thống kê học tập hàng tuần
     */
    public function sendWeeklyStats($webhookUrl, $subject = null)
    {
        if ($subject) {
            // Thống kê cho một môn học cụ thể
            $stats = $this->getSubjectWeeklyStats($subject);
            $embed = $this->createSubjectStatsEmbed($subject, $stats);
        } else {
            // Thống kê tổng quan
            $stats = $this->getOverallWeeklyStats();
            $embed = $this->createOverallStatsEmbed($stats);
        }

        $content = "📊 **Báo cáo học tập tuần này**";
        return $this->sendWebhookMessage($webhookUrl, $content, [$embed]);
    }

    /**
     * Lấy thống kê tuần của một môn học
     */
    private function getSubjectWeeklyStats($subject)
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();

        return [
            'new_lessons' => Lesson::whereHas('chapter', function($query) use ($subject) {
                $query->where('subject_id', $subject->id);
            })->whereBetween('created_at', [$startOfWeek, $endOfWeek])->count(),
            
            'total_lessons' => Lesson::whereHas('chapter', function($query) use ($subject) {
                $query->where('subject_id', $subject->id);
            })->count(),
            
            'chapters_count' => $subject->chapters()->count(),
        ];
    }

    /**
     * Lấy thống kê tổng quan tuần
     */
    private function getOverallWeeklyStats()
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();

        return [
            'new_lessons' => Lesson::whereBetween('created_at', [$startOfWeek, $endOfWeek])->count(),
            'total_lessons' => Lesson::count(),
            'total_subjects' => Subject::count(),
            'total_chapters' => \App\Models\Chapter::count(),
        ];
    }

    /**
     * Tạo embed thống kê cho môn học
     */
    private function createSubjectStatsEmbed($subject, $stats)
    {
        return [
            'title' => '📊 Thống kê tuần - ' . $subject->title,
            'color' => 0x10b981, // Green
            'fields' => [
                [
                    'name' => '📝 Bài học mới tuần này',
                    'value' => $stats['new_lessons'] . ' bài',
                    'inline' => true
                ],
                [
                    'name' => '📚 Tổng số bài học',
                    'value' => $stats['total_lessons'] . ' bài',
                    'inline' => true
                ],
                [
                    'name' => '📖 Số chương',
                    'value' => $stats['chapters_count'] . ' chương',
                    'inline' => true
                ],
                [
                    'name' => '👨‍🏫 Giáo viên',
                    'value' => $subject->instructor_name ?: 'Chưa cập nhật',
                    'inline' => true
                ]
            ],
            'footer' => [
                'text' => site_title() . ' - Báo cáo tuần',
                'icon_url' => site_logo() ?: null
            ],
            'timestamp' => now()->toISOString(),
            'url' => route('user.subjects.show', $subject)
        ];
    }

    /**
     * Tạo embed thống kê tổng quan
     */
    private function createOverallStatsEmbed($stats)
    {
        return [
            'title' => '📊 Thống kê tổng quan tuần này',
            'description' => 'Báo cáo hoạt động học tập trên toàn hệ thống',
            'color' => 0x3b82f6, // Blue
            'fields' => [
                [
                    'name' => '📝 Bài học mới',
                    'value' => $stats['new_lessons'] . ' bài',
                    'inline' => true
                ],
                [
                    'name' => '📚 Tổng bài học',
                    'value' => $stats['total_lessons'] . ' bài',
                    'inline' => true
                ],
                [
                    'name' => '🎓 Tổng môn học',
                    'value' => $stats['total_subjects'] . ' môn',
                    'inline' => true
                ],
                [
                    'name' => '📖 Tổng chương',
                    'value' => $stats['total_chapters'] . ' chương',
                    'inline' => true
                ]
            ],
            'footer' => [
                'text' => site_title() . ' - Báo cáo hệ thống',
                'icon_url' => site_logo() ?: null
            ],
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * Gửi thông báo bảo trì
     */
    public function sendMaintenanceNotification($webhookUrl, $startTime, $endTime, $reason = null)
    {
        $embed = [
            'title' => '🔧 Thông báo bảo trì hệ thống',
            'description' => 'Hệ thống sẽ tạm ngừng hoạt động để bảo trì',
            'color' => 0xf59e0b, // Yellow
            'fields' => [
                [
                    'name' => '⏰ Thời gian bắt đầu',
                    'value' => $startTime->format('d/m/Y H:i'),
                    'inline' => true
                ],
                [
                    'name' => '⏰ Thời gian kết thúc',
                    'value' => $endTime->format('d/m/Y H:i'),
                    'inline' => true
                ],
                [
                    'name' => '⏱️ Thời gian dự kiến',
                    'value' => $startTime->diffInMinutes($endTime) . ' phút',
                    'inline' => true
                ]
            ],
            'footer' => [
                'text' => site_title() . ' - Thông báo bảo trì',
                'icon_url' => site_logo() ?: null
            ],
            'timestamp' => now()->toISOString()
        ];

        if ($reason) {
            $embed['fields'][] = [
                'name' => '📋 Lý do bảo trì',
                'value' => $reason,
                'inline' => false
            ];
        }

        return $this->sendWebhookMessage($webhookUrl, '🔧 **Thông báo bảo trì hệ thống**', [$embed]);
    }
}
