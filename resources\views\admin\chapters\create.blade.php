<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-plus-circle" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.chapters.index') }}"
               style="background-color: #6b7280; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#4b5563'"
               onmouseout="this.style.backgroundColor='#6b7280'">
                <i class="fas fa-arrow-left" style="margin-right: 8px;"></i>
                Quay Lại
            </a>
        </div>
    </x-slot>

    <!-- Form Card -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        <div style="padding: 32px;">
            <form method="POST" action="{{ route('admin.chapters.store') }}">
                @csrf

                <!-- Chọn Môn Học -->
                <div style="margin-bottom: 24px;">
                    <label for="subject_id" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-book" style="margin-right: 8px; color: #2563eb;"></i>
                        Chọn Môn Học *
                    </label>
                    <select id="subject_id"
                            name="subject_id"
                            required
                            style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                            onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                            onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                        <option value="">-- Chọn Môn Học --</option>
                        @foreach($subjects as $subject)
                            <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                {{ $subject->title }}
                            </option>
                        @endforeach
                    </select>
                    @error('subject_id')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Tên Chương -->
                <div style="margin-bottom: 24px;">
                    <label for="title" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-list-alt" style="margin-right: 8px; color: #2563eb;"></i>
                        Tên Chương *
                    </label>
                    <input type="text"
                           id="title"
                           name="title"
                           value="{{ old('title') }}"
                           required
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="Nhập Tên Chương (VD: Chương 1: Giới Thiệu...)">
                    @error('title')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Thứ Tự -->
                <div style="margin-bottom: 32px;">
                    <label for="order" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-sort-numeric-up" style="margin-right: 8px; color: #2563eb;"></i>
                        Thứ Tự Chương (Tùy Chọn)
                    </label>
                    <input type="number"
                           id="order"
                           name="order"
                           value="{{ old('order') }}"
                           min="1"
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="Nhập Số Thứ Tự (VD: 1, 2, 3...)">
                    <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                        <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                        Để Trống Để Tự Động Sắp Xếp Theo Thứ Tự Tạo
                    </div>
                    @error('order')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 16px; justify-content: flex-end; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                    <a href="{{ route('admin.chapters.index') }}"
                       style="background-color: #f3f4f6; color: #374151; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.backgroundColor='#e5e7eb'"
                       onmouseout="this.style.backgroundColor='#f3f4f6'">
                        <i class="fas fa-times" style="margin-right: 8px;"></i>
                        Hủy Bỏ
                    </a>
                    <button type="submit"
                            style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'"
                            onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                        <i class="fas fa-save" style="margin-right: 8px;"></i>
                        Tạo Chương
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Info Card -->
    <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 12px; padding: 24px; margin-top: 24px;">
        <div style="display: flex; align-items: start; gap: 16px;">
            <div style="width: 48px; height: 48px; background-color: #0ea5e9; border-radius: 12px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                <i class="fas fa-lightbulb" style="font-size: 20px; color: #ffffff;"></i>
            </div>
            <div>
                <h3 style="font-size: 1.125rem; font-weight: 600; color: #0c4a6e; margin: 0 0 8px 0;">
                    Mẹo Tổ Chức Chương
                </h3>
                <ul style="color: #0369a1; margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li>Đặt Tên Chương Rõ Ràng Và Dễ Hiểu</li>
                    <li>Sắp Xếp Chương Theo Thứ Tự Logic Từ Cơ Bản Đến Nâng Cao</li>
                    <li>Mỗi Chương Nên Có 3-7 Bài Học Để Dễ Theo Dõi</li>
                    <li>Sử Dụng Số Thứ Tự Để Tạo Cấu Trúc Rõ Ràng</li>
                </ul>
            </div>
        </div>
    </div>
</x-admin-layout>