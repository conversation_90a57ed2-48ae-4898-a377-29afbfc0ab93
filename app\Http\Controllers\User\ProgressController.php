<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Models\Chapter;
use App\Models\Lesson;
use Illuminate\Http\Request;

class ProgressController extends Controller
{
    /**
     * Hiển Thị Tiến Độ Học Tập
     */
    public function index()
    {
        $user = auth()->user();

        // Thống kê tổng quan
        $totalSubjects = Subject::count();
        $totalLessons = Lesson::count();

        // Giả lập tiến độ (sau này có thể tạo bảng user_progress)
        $completedLessons = 0; // Tạm thời = 0
        $progressPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100, 1) : 0;

        // Lấy các môn học với tiến độ
        $subjects = Subject::withCount(['chapters', 'lessons'])
            ->orderBy('title')
            ->get()
            ->map(function($subject) {
                // <PERSON><PERSON><PERSON> lập tiến độ cho mỗi môn học
                $subject->progress_percentage = rand(0, 100);
                $subject->completed_lessons = rand(0, $subject->lessons_count);
                return $subject;
            });

        return view('user.progress.index', compact(
            'totalSubjects',
            'totalLessons',
            'completedLessons',
            'progressPercentage',
            'subjects'
        ));
    }
}
