<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-list-alt" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.chapters.create') }}"
               style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#1d4ed8'"
               onmouseout="this.style.backgroundColor='#2563eb'">
                <i class="fas fa-plus" style="margin-right: 8px;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </a>
        </div>
    </x-slot>

    <!-- Success Message -->
    @if(session('success'))
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; border-radius: 8px; padding: 16px; margin-bottom: 24px; color: #15803d;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            {{ session('success') }}
        </div>
    @endif

    <!-- Chapters List -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        @if($chapters->count() > 0)
            <!-- Table Header -->
            <div style="background-color: #f8fafc; padding: 16px 24px; border-bottom: 1px solid #e5e7eb;">
                <div style="display: grid; grid-template-columns: 60px 200px 1fr 100px 120px 150px; gap: 16px; align-items: center; font-weight: 600; color: #374151; font-size: 0.875rem;">
                    <div>STT</div>
                    <div>
                        <i class="fas fa-book" style="margin-right: 4px;"></i>
                        Môn Học
                    </div>
                    <div>Thông Tin Chương</div>
                    <div style="text-align: center;">
                        <i class="fas fa-sort-numeric-up" style="margin-right: 4px;"></i>
                        Thứ Tự
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-play-circle" style="margin-right: 4px;"></i>
                        Bài Học
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-cogs" style="margin-right: 4px;"></i>
                        Thao Tác
                    </div>
                </div>
            </div>

            <!-- Table Body -->
            <div>
                @foreach($chapters as $index => $chapter)
                    <div style="padding: 20px 24px; border-bottom: 1px solid #f3f4f6; {{ $loop->last ? 'border-bottom: none;' : '' }}">
                        <div style="display: grid; grid-template-columns: 60px 200px 1fr 100px 120px 150px; gap: 16px; align-items: center;">
                            <!-- STT -->
                            <div style="font-weight: 600; color: #6b7280;">
                                {{ $index + 1 }}
                            </div>

                            <!-- Môn Học -->
                            <div>
                                <div style="background-color: #dbeafe; color: #2563eb; padding: 8px 12px; border-radius: 8px; font-size: 0.875rem; font-weight: 600; text-align: center;">
                                    <i class="fas fa-book" style="margin-right: 4px;"></i>
                                    {{ $chapter->subject->title }}
                                </div>
                            </div>

                            <!-- Thông Tin Chương -->
                            <div>
                                <h3 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 1rem;">
                                    {{ $chapter->title }}
                                </h3>
                                <p style="color: #9ca3af; margin: 0; font-size: 0.75rem;">
                                    <i class="fas fa-calendar-alt" style="margin-right: 4px;"></i>
                                    Tạo: {{ $chapter->created_at->format('d/m/Y H:i') }}
                                </p>
                            </div>

                            <!-- Thứ Tự -->
                            <div style="text-align: center;">
                                <span style="background-color: #f3f4f6; color: #374151; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $chapter->order }}
                                </span>
                            </div>

                            <!-- Số Bài Học -->
                            <div style="text-align: center;">
                                <span style="background-color: #d1fae5; color: #059669; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $chapter->lessons_count }}
                                </span>
                            </div>

                            <!-- Thao Tác -->
                            <div style="display: flex; gap: 8px; justify-content: center;">
                                <a href="{{ route('admin.chapters.show', $chapter) }}"
                                   style="background-color: #f3f4f6; color: #374151; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                                   onmouseout="this.style.backgroundColor='#f3f4f6'"
                                   title="Xem Chi Tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.chapters.edit', $chapter) }}"
                                   style="background-color: #fef3c7; color: #d97706; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#fde68a'"
                                   onmouseout="this.style.backgroundColor='#fef3c7'"
                                   title="Chỉnh Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ route('admin.chapters.destroy', $chapter) }}" style="display: inline; margin: 0;"
                                      onsubmit="return confirm('Bạn Có Chắc Muốn Xóa Chương Này? Tất Cả Bài Học Trong Chương Sẽ Bị Xóa!')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            style="background-color: #fecaca; color: #dc2626; padding: 8px 12px; border-radius: 6px; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.3s ease;"
                                            onmouseover="this.style.backgroundColor='#fca5a5'"
                                            onmouseout="this.style.backgroundColor='#fecaca'"
                                            title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div style="text-align: center; padding: 60px 20px;">
                <i class="fas fa-list-alt" style="font-size: 64px; color: #d1d5db; margin-bottom: 16px; display: block;"></i>
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #374151; margin: 0 0 8px 0;">
                    Chưa Có Chương Nào
                </h3>
                <p style="color: #6b7280; margin: 0 0 24px 0;">
                    Hãy Tạo Chương Đầu Tiên Để Bắt Đầu Tổ Chức Nội Dung Học Tập
                </p>
                <a href="{{ route('admin.chapters.create') }}"
                   style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'"
                   onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-plus" style="margin-right: 8px;"></i>
                    Tạo Chương Đầu Tiên
                </a>
            </div>
        @endif
    </div>

    <!-- Quick Stats -->
    <div style="margin-top: 32px; display: grid; gap: 20px; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
        <!-- Total Chapters by Subject -->
        @php
            $subjectStats = $chapters->groupBy('subject.title')->map(function($chapters) {
                return $chapters->count();
            });
        @endphp

        @if($subjectStats->count() > 0)
            @foreach($subjectStats as $subjectTitle => $chapterCount)
                <div style="background-color: #ffffff; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                                {{ $subjectTitle }}
                            </h4>
                            <p style="font-size: 1.5rem; font-weight: bold; color: #2563eb; margin: 0;">
                                {{ $chapterCount }} Chương
                            </p>
                        </div>
                        <div style="width: 40px; height: 40px; background-color: #dbeafe; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-list-alt" style="font-size: 18px; color: #2563eb;"></i>
                        </div>
                    </div>
                </div>
            @endforeach
        @endif
    </div>
</x-admin-layout>