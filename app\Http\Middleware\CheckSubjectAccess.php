<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Subject;

class CheckSubjectAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Lấy subject từ route parameter
        $subject = $request->route('subject');

        // Nếu không có subject trong route, cho phép tiếp tục
        if (!$subject) {
            return $next($request);
        }

        // Nếu subject là ID, lấy model
        if (is_numeric($subject)) {
            $subject = Subject::find($subject);
        }

        // Nếu không tìm thấy subject, trả về 404
        if (!$subject) {
            abort(404, 'Môn <PERSON>ông <PERSON>ồ<PERSON>');
        }

        // Nếu subject miễn phí, cho phép truy cập
        if ($subject->isFree()) {
            return $next($request);
        }

        // Kiểm tra user đã đăng nhập chưa
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Vui Lòng Đăng Nhập Để Truy Cập Môn Học Này');
        }

        // Kiểm tra user có quyền truy cập subject không
        if (!$subject->canUserAccess(auth()->id())) {
            // Nếu đang truy cập lesson, redirect về subject detail với thông báo
            if ($request->route()->getName() === 'user.subjects.lesson') {
                return redirect()->route('user.subjects.show', $subject)
                    ->with('error', 'Bạn Cần Mua Môn Học Này Để Truy Cập Bài Học. Giá: ' . $subject->formatted_price);
            }

            // Nếu đang truy cập subject detail, cho phép xem nhưng không cho vào lesson
            if ($request->route()->getName() === 'user.subjects.show') {
                return $next($request);
            }

            // Các trường hợp khác, redirect về danh sách subjects
            return redirect()->route('user.subjects.index')
                ->with('error', 'Bạn Cần Mua Môn Học Này Để Truy Cập. Giá: ' . $subject->formatted_price);
        }

        return $next($request);
    }
}
