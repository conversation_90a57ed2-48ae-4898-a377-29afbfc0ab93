<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Models\User;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SubjectPurchaseController extends Controller
{
    /**
     * Hiển <PERSON>hị Trang Mua Subject
     */
    public function show(Subject $subject)
    {
        // Kiểm tra subject có tồn tại và có giá không
        if ($subject->isFree()) {
            return redirect()->route('user.subjects.show', $subject)
                ->with('info', '<PERSON><PERSON>n <PERSON>, <PERSON>ạn <PERSON>');
        }

        // Kiểm tra user đã mua chưa
        if (auth()->check() && $subject->isPurchasedByUser(auth()->id())) {
            return redirect()->route('user.subjects.show', $subject)
                ->with('info', 'Bạn <PERSON>a Môn <PERSON>');
        }

        $user = auth()->user();

        return view('user.subjects.purchase', compact('subject', 'user'));
    }

    /**
     * Xử Lý Mua Subject Bằng Số Dư
     */
    public function purchaseWithBalance(Request $request, Subject $subject)
    {
        $validator = Validator::make($request->all(), [
            'confirm_purchase' => 'required|accepted',
        ], [
            'confirm_purchase.required' => 'Vui Lòng Xác Nhận Mua Môn Học',
            'confirm_purchase.accepted' => 'Vui Lòng Xác Nhận Mua Môn Học',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Kiểm tra subject có tồn tại và có giá không
        if ($subject->isFree()) {
            return redirect()->route('user.subjects.show', $subject)
                ->with('error', 'Môn Học Này Miễn Phí, Không Cần Mua');
        }

        // Kiểm tra user đã mua chưa
        if ($subject->isPurchasedByUser(auth()->id())) {
            return redirect()->route('user.subjects.show', $subject)
                ->with('error', 'Bạn Đã Mua Môn Học Này Rồi');
        }

        $user = auth()->user();

        // Kiểm tra số dư
        if ($user->balance < $subject->price) {
            return redirect()->back()
                ->with('error', 'Số Dư Không Đủ. Số Dư Hiện Tại: ' . number_format($user->balance, 0, ',', '.') . ' VND. Cần: ' . $subject->formatted_price);
        }

        try {
            DB::beginTransaction();

            // Trừ tiền user
            $user->update([
                'balance' => $user->balance - $subject->price
            ]);

            // Tạo transaction
            $transactionCode = 'SUB_' . strtoupper(bin2hex(random_bytes(4))) . '_' . time();

            Transaction::create([
                'user_id' => $user->id,
                'transaction_id' => $transactionCode,
                'transaction_code' => $transactionCode,
                'type' => 'subject_purchase',
                'balance_before' => $user->balance + $subject->price,
                'balance_after' => $user->balance,
                'amount' => $subject->price,
                'description' => 'Mua Môn Học: ' . $subject->title,
                'ip' => request()->ip(),
                'data' => json_encode([
                    'subject_id' => $subject->id,
                    'subject_title' => $subject->title,
                    'subject_price' => $subject->price,
                ]),
                'status' => 'success',
                'domain' => request()->getHost()
            ]);

            // Tạo bản ghi mua subject
            $user->purchaseSubject(
                $subject->id,
                $subject->price,
                'balance',
                $transactionCode
            );

            DB::commit();

            return redirect()->route('user.subjects.show', $subject)
                ->with('success', 'Mua Môn Học Thành Công! Bạn Có Thể Truy Cập Tất Cả Bài Học Ngay Bây Giờ.');

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'Có Lỗi Xảy Ra: ' . $e->getMessage());
        }
    }

    /**
     * Lịch Sử Mua Hàng
     */
    public function purchaseHistory()
    {
        $user = auth()->user();

        $purchases = $user->userSubjects()
            ->with('subject')
            ->byDomain()
            ->orderBy('purchased_at', 'desc')
            ->paginate(10);

        return view('user.subjects.purchase-history', compact('purchases'));
    }
}
