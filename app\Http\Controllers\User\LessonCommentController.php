<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\LessonComment;
use Illuminate\Http\Request;

class LessonCommentController extends Controller
{
    /**
     * Thêm comment mới
     */
    public function store(Request $request, Lesson $lesson)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:lesson_comments,id',
        ]);

        $comment = LessonComment::create([
            'lesson_id' => $lesson->id,
            'user_id' => auth()->id(),
            'content' => $request->content,
            'parent_id' => $request->parent_id,
        ]);

        $comment->load('user', 'replies');

        return response()->json([
            'success' => true,
            'comment' => $comment,
            'message' => 'Bình luận đã được thêm thành công!'
        ]);
    }

    /**
     * Xóa comment
     */
    public function destroy(LessonComment $comment)
    {
        // <PERSON><PERSON><PERSON> tra quyền xóa (chỉ người tạo comment)
        if ($comment->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền xóa bình luận này!'
            ], 403);
        }

        $comment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Bình luận đã được xóa thành công!'
        ]);
    }
}
