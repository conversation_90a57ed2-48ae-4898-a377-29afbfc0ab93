<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>Trang Quản Trị - {{ site_title() }}</title>

        <!-- SEO Meta Tags -->
        <meta name="description" content="{{ site_description() }}">
        <meta name="keywords" content="{{ site_keywords() }}">
        <meta name="author" content="{{ site_author() }}">

        <!-- Favicon -->
        @if(site_favicon())
            <link rel="icon" type="image/x-icon" href="{{ site_favicon() }}">
            <link rel="shortcut icon" type="image/x-icon" href="{{ site_favicon() }}">
        @endif

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc;">
        <div style="display: flex; min-height: 100vh;">
            <!-- Admin Sidebar -->
            <div style="width: 280px; background-color: #1f2937; color: #ffffff; position: fixed; height: 100vh; overflow-y: auto; box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);">
                <!-- Logo -->
                <div style="padding: 24px 20px; border-bottom: 1px solid #374151;">
                    @if(site_logo())
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                            <img src="{{ site_logo() }}"
                                 alt="{{ site_title() }}"
                                 style="height: 40px; width: auto; object-fit: contain;">
                            <div>
                                <h1 style="font-size: 1.25rem; font-weight: bold; color: #ffffff; margin: 0;">
                                    Quản Trị
                                </h1>
                            </div>
                        </div>
                    @else
                        <h1 style="font-size: 1.5rem; font-weight: bold; color: #ffffff; margin: 0 0 8px 0;">
                            <i class="fas fa-crown" style="margin-right: 8px;"></i>
                            {{ site_title() }} Quản Trị Viên
                        </h1>
                    @endif
                    <p style="font-size: 0.875rem; color: #9ca3af; margin: 0;">
                        Khu Vực Quản Trị Viên
                    </p>
                </div>

                <!-- Admin Info -->
                <div style="padding: 20px; border-bottom: 1px solid #374151;">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background-color: #dc2626; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                            {{ substr(auth()->user()->name, 0, 1) }}
                        </div>
                        <div>
                            <p style="font-weight: 600; color: #ffffff; margin: 0; font-size: 0.875rem;">
                                {{ auth()->user()->name }}
                            </p>
                            <p style="font-size: 0.75rem; color: #9ca3af; margin: 0;">
                                Quản Trị Viên
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Admin Navigation Menu -->
                <nav style="padding: 20px 0;">
                    <ul style="list-style: none; margin: 0; padding: 0;">
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('admin.dashboard') }}" 
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #e5e7eb; text-decoration: none; transition: all 0.3s ease; {{ request()->routeIs('admin.dashboard') ? 'background-color: #374151; color: #ffffff; border-right: 3px solid #dc2626;' : '' }}"
                               onmouseover="if (!this.style.backgroundColor.includes('374151')) { this.style.backgroundColor='#374151'; this.style.color='#ffffff'; }"
                               onmouseout="if (!this.style.backgroundColor.includes('374151') || !this.style.borderRight.includes('dc2626')) { this.style.backgroundColor='transparent'; this.style.color='#e5e7eb'; }">
                                <i class="fas fa-home" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Bảng Điều Khiển</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="#" 
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #e5e7eb; text-decoration: none; transition: all 0.3s ease;"
                               onmouseover="this.style.backgroundColor='#374151'; this.style.color='#ffffff';"
                               onmouseout="this.style.backgroundColor='transparent'; this.style.color='#e5e7eb';">
                                <i class="fas fa-book" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Quản Lý Môn Học</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="#" 
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #e5e7eb; text-decoration: none; transition: all 0.3s ease;"
                               onmouseover="this.style.backgroundColor='#374151'; this.style.color='#ffffff';"
                               onmouseout="this.style.backgroundColor='transparent'; this.style.color='#e5e7eb';">
                                <i class="fas fa-bookmark" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Quản Lý Chương</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="#" 
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #e5e7eb; text-decoration: none; transition: all 0.3s ease;"
                               onmouseover="this.style.backgroundColor='#374151'; this.style.color='#ffffff';"
                               onmouseout="this.style.backgroundColor='transparent'; this.style.color='#e5e7eb';">
                                <i class="fas fa-file-alt" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Quản Lý Bài Học</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="#" 
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #e5e7eb; text-decoration: none; transition: all 0.3s ease;"
                               onmouseover="this.style.backgroundColor='#374151'; this.style.color='#ffffff';"
                               onmouseout="this.style.backgroundColor='transparent'; this.style.color='#e5e7eb';">
                                <i class="fas fa-users" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Quản Lý Người Dùng</span>
                            </a>
                        </li>
                        
                        <li style="margin: 20px 0 4px 0; padding: 0 20px;">
                            <hr style="border: none; border-top: 1px solid #374151; margin: 0;">
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('admin.statistics.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: {{ request()->routeIs('admin.statistics.*') ? '#ffffff' : '#e5e7eb' }}; text-decoration: none; transition: all 0.3s ease; background-color: {{ request()->routeIs('admin.statistics.*') ? '#374151' : 'transparent' }};"
                               onmouseover="if (!this.style.backgroundColor.includes('#374151')) { this.style.backgroundColor='#374151'; this.style.color='#ffffff'; }"
                               onmouseout="if (!{{ request()->routeIs('admin.statistics.*') ? 'true' : 'false' }}) { this.style.backgroundColor='transparent'; this.style.color='#e5e7eb'; }">
                                <i class="fas fa-chart-bar" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Thống Kê</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('admin.settings.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: {{ request()->routeIs('admin.settings.*') ? '#ffffff' : '#e5e7eb' }}; text-decoration: none; transition: all 0.3s ease; background-color: {{ request()->routeIs('admin.settings.*') ? '#374151' : 'transparent' }};"
                               onmouseover="if (!this.style.backgroundColor.includes('#374151')) { this.style.backgroundColor='#374151'; this.style.color='#ffffff'; }"
                               onmouseout="if (!{{ request()->routeIs('admin.settings.*') ? 'true' : 'false' }}) { this.style.backgroundColor='transparent'; this.style.color='#e5e7eb'; }">
                                <i class="fas fa-cog" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Cài Đặt Hệ Thống</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('admin.bots.index') }}"
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: {{ request()->routeIs('admin.bots.*') ? '#ffffff' : '#e5e7eb' }}; text-decoration: none; transition: all 0.3s ease; background-color: {{ request()->routeIs('admin.bots.*') ? '#374151' : 'transparent' }};"
                               onmouseover="if (!this.style.backgroundColor.includes('#374151')) { this.style.backgroundColor='#374151'; this.style.color='#ffffff'; }"
                               onmouseout="if (!{{ request()->routeIs('admin.bots.*') ? 'true' : 'false' }}) { this.style.backgroundColor='transparent'; this.style.color='#e5e7eb'; }">
                                <i class="fas fa-robot" style="width: 20px; text-align: center;"></i>
                                <span style="font-weight: 500;">Bot Telegram & Discord</span>
                            </a>
                        </li>


                        <li style="margin: 20px 0 4px 0; padding: 0 20px;">
                            <hr style="border: none; border-top: 1px solid #374151; margin: 0;">
                        </li>
                        <li style="margin-bottom: 4px;">
                            <a href="{{ route('dashboard') }}" 
                               style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #10b981; text-decoration: none; transition: all 0.3s ease; font-weight: 600;"
                               onmouseover="this.style.backgroundColor='#065f46'; this.style.color='#ffffff';"
                               onmouseout="this.style.backgroundColor='transparent'; this.style.color='#10b981';">
                                <i class="fas fa-graduation-cap" style="width: 20px; text-align: center;"></i>
                                <span>Về Khu Vực Người Dùng</span>
                            </a>
                        </li>
                        <li style="margin-bottom: 4px;">
                            <form method="POST" action="{{ route('logout') }}" style="margin: 0;">
                                @csrf
                                <button type="submit" 
                                        style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #ef4444; background: none; border: none; width: 100%; text-align: left; cursor: pointer; transition: all 0.3s ease; font-family: inherit; font-size: inherit;"
                                        onmouseover="this.style.backgroundColor='#7f1d1d'; this.style.color='#ffffff';"
                                        onmouseout="this.style.backgroundColor='transparent'; this.style.color='#ef4444';">
                                    <i class="fas fa-sign-out-alt" style="width: 20px; text-align: center;"></i>
                                    <span style="font-weight: 500;">Đăng Xuất</span>
                                </button>
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- Main Content -->
            <div style="margin-left: 280px; flex: 1; display: flex; flex-direction: column;">
                <!-- Top Header -->
                @isset($header)
                <header style="background-color: #ffffff; border-bottom: 1px solid #e5e7eb; padding: 16px 32px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                    {{ $header }}
                </header>
                @endisset

                <!-- Page Content -->
                <main style="flex: 1; padding: 32px;">
                    {{ $slot }}
                </main>
            </div>
        </div>
    </body>
</html>
