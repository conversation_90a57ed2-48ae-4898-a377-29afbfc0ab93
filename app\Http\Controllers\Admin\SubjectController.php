<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SubjectController extends Controller
{
    /**
     * Hiển <PERSON>ch <PERSON>
     */
    public function index()
    {
        $subjects = Subject::withCount(['chapters', 'lessons'])->latest()->get();
        return view('admin.subjects.index', compact('subjects'));
    }

    /**
     * Hiển Thị Form Tạo Môn H<PERSON>
     */
    public function create()
    {
        return view('admin.subjects.create');
    }

    /**
     * <PERSON><PERSON><PERSON>
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'group_link' => 'nullable|url',
            'price' => 'nullable|numeric|min:0',
            'is_free' => 'nullable|boolean',
            'instructor_name' => 'nullable|string|max:255',
            'telegram_group_id' => 'nullable|string|max:255',
            'discord_webhook_url' => 'nullable|url|max:500',
            'auto_notify' => 'nullable|boolean',
        ]);

        $data = $request->only(['title', 'description', 'group_link', 'price', 'instructor_name', 'telegram_group_id', 'discord_webhook_url']);

        // Xử lý logic miễn phí
        $data['is_free'] = $request->has('is_free') ? true : false;
        if ($data['is_free']) {
            $data['price'] = 0;
        }

        // Xử lý auto notify
        $data['auto_notify'] = $request->has('auto_notify') ? true : false;

        // Xử Lý Upload Thumbnail
        if ($request->hasFile('thumbnail')) {
            $data['thumbnail'] = $request->file('thumbnail')->store('subjects', 'public');
        }

        Subject::create($data);

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Môn Học Đã Được Tạo Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Môn Học
     */
    public function show(Subject $subject)
    {
        $subject->load(['chapters.lessons']);
        return view('admin.subjects.show', compact('subject'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa Môn Học
     */
    public function edit(Subject $subject)
    {
        return view('admin.subjects.edit', compact('subject'));
    }

    /**
     * Cập Nhật Môn Học
     */
    public function update(Request $request, Subject $subject)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'group_link' => 'nullable|url',
            'price' => 'nullable|numeric|min:0',
            'is_free' => 'nullable|boolean',
            'instructor_name' => 'nullable|string|max:255',
            'telegram_group_id' => 'nullable|string|max:255',
            'discord_webhook_url' => 'nullable|url|max:500',
            'auto_notify' => 'nullable|boolean',
        ]);

        $data = $request->only(['title', 'description', 'group_link', 'price', 'instructor_name', 'telegram_group_id', 'discord_webhook_url']);

        // Xử lý logic miễn phí
        $data['is_free'] = $request->has('is_free') ? true : false;
        if ($data['is_free']) {
            $data['price'] = 0;
        }

        // Xử lý auto notify
        $data['auto_notify'] = $request->has('auto_notify') ? true : false;

        // Xử Lý Upload Thumbnail Mới
        if ($request->hasFile('thumbnail')) {
            // Xóa Thumbnail Cũ
            if ($subject->thumbnail) {
                Storage::disk('public')->delete($subject->thumbnail);
            }
            $data['thumbnail'] = $request->file('thumbnail')->store('subjects', 'public');
        }

        $subject->update($data);

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Môn Học Đã Được Cập Nhật Thành Công!');
    }

    /**
     * Xóa Môn Học
     */
    public function destroy(Subject $subject)
    {
        // Xóa Thumbnail
        if ($subject->thumbnail) {
            Storage::disk('public')->delete($subject->thumbnail);
        }

        $subject->delete();

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Môn Học Đã Được Xóa Thành Công!');
    }
}
