<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\UserLessonProgress;
use Illuminate\Http\Request;

class LessonProgressController extends Controller
{
    /**
     * Đánh dấu bài học đã hoàn thành
     */
    public function markCompleted(Request $request, Lesson $lesson)
    {
        $progress = UserLessonProgress::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'lesson_id' => $lesson->id,
            ],
            [
                'is_completed' => true,
                'completed_at' => now(),
            ]
        );

        return response()->json([
            'success' => true,
            'message' => '<PERSON><PERSON><PERSON> học đã được đánh dấu hoàn thành!',
            'progress' => $progress
        ]);
    }

    /**
     * Bỏ đánh dấu hoàn thành
     */
    public function markIncomplete(Request $request, Lesson $lesson)
    {
        $progress = UserLessonProgress::where([
            'user_id' => auth()->id(),
            'lesson_id' => $lesson->id,
        ])->first();

        if ($progress) {
            $progress->update([
                'is_completed' => false,
                'completed_at' => null,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã bỏ đánh dấu hoàn thành!',
            'progress' => $progress
        ]);
    }

    /**
     * Cập nhật thời gian xem
     */
    public function updateWatchTime(Request $request, Lesson $lesson)
    {
        $request->validate([
            'watch_time' => 'required|integer|min:0',
        ]);

        $progress = UserLessonProgress::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'lesson_id' => $lesson->id,
            ],
            [
                'watch_time' => $request->watch_time,
            ]
        );

        return response()->json([
            'success' => true,
            'progress' => $progress
        ]);
    }
}
