<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // Tên <PERSON>
            $table->text('description')->nullable(); // Mô <PERSON>
            $table->string('thumbnail')->nullable(); // Ảnh Thumbnail
            $table->string('group_link')->nullable(); // Link Nhóm Telegram
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subjects');
    }
};
