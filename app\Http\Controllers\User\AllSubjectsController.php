<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use Illuminate\Http\Request;

class AllSubjectsController extends Controller
{
    /**
     * <PERSON><PERSON><PERSON>t <PERSON>
     */
    public function index(Request $request)
    {
        $query = Subject::with(['chapters.lessons'])
            ->withCount(['chapters', 'lessons']);

        // Filter by price
        if ($request->has('filter')) {
            switch ($request->filter) {
                case 'free':
                    $query->where('is_free', true);
                    break;
                case 'paid':
                    $query->where('is_free', false);
                    break;
            }
        }

        // Search
        if ($request->has('search') && !empty($request->search)) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'title');
        $sortOrder = $request->get('order', 'asc');

        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'lessons':
                $query->orderBy('lessons_count', $sortOrder);
                break;
            case 'created':
                $query->orderBy('created_at', $sortOrder);
                break;
            default:
                $query->orderBy('title', $sortOrder);
        }

        $subjects = $query->paginate(12);

        return view('user.all-subjects.index', compact('subjects'));
    }
}
