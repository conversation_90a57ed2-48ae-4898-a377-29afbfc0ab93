<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subject extends Model
{
    protected $fillable = [
        'title',
        'description',
        'thumbnail',
        'group_link',
        'price',
        'is_free',
        'instructor_name',
        'telegram_group_id',
        'discord_webhook_url',
        'auto_notify',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_free' => 'boolean',
    ];

    /**
     * Relationship: M<PERSON>t <PERSON><PERSON>
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(Chapter::class)->orderBy('order');
    }

    /**
     * Relationship: Một <PERSON> (Thông Qua Chương)
     */
    public function lessons()
    {
        return $this->hasManyThrough(Lesson::class, Chapter::class);
    }

    /**
     * Lấy Gi<PERSON>
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->is_free || $this->price == 0) {
            return 'Miễ<PERSON>';
        }

        return number_format($this->price, 0, ',', '.') . ' VND';
    }

    /**
     * Kiểm Tra Khóa Học Miễn Phí
     */
    public function isFree(): bool
    {
        return $this->is_free || $this->price == 0;
    }

    /**
     * Lấy Giá Hiển Thị
     */
    public function getPriceDisplayAttribute(): string
    {
        return $this->formatted_price;
    }

    /**
     * Lấy Tổng Số Chương
     */
    public function getTotalChaptersAttribute(): int
    {
        return $this->chapters()->count();
    }

    /**
     * Lấy Tổng Số Bài Học
     */
    public function getTotalLessonsAttribute(): int
    {
        return $this->lessons()->count();
    }

    /**
     * Relationship: Subject Có Nhiều UserSubject (Người Dùng Đã Mua)
     */
    public function userSubjects()
    {
        return $this->hasMany(UserSubject::class);
    }

    /**
     * Relationship: Subject Có Nhiều Users Đã Mua (Thông Qua UserSubject)
     */
    public function purchasedByUsers()
    {
        return $this->belongsToMany(User::class, 'user_subjects')
            ->withPivot(['amount_paid', 'payment_method', 'transaction_code', 'status', 'purchased_at', 'expires_at'])
            ->withTimestamps();
    }

    /**
     * Kiểm Tra User Đã Mua Subject Này Chưa
     */
    public function isPurchasedByUser($userId): bool
    {
        if ($this->isFree()) {
            return true; // Khóa học miễn phí thì ai cũng có thể truy cập
        }

        return $this->userSubjects()
            ->byUser($userId)
            ->completed()
            ->byDomain()
            ->exists();
    }

    /**
     * Kiểm Tra User Có Thể Truy Cập Subject Này Không
     */
    public function canUserAccess($userId): bool
    {
        if ($this->isFree()) {
            return true; // Khóa học miễn phí thì ai cũng có thể truy cập
        }

        $userSubject = $this->userSubjects()
            ->byUser($userId)
            ->completed()
            ->byDomain()
            ->first();

        if (!$userSubject) {
            return false; // Chưa mua
        }

        return $userSubject->canAccess(); // Kiểm tra có hết hạn không
    }

    /**
     * Lấy Thông Tin Mua Hàng Của User
     */
    public function getUserPurchaseInfo($userId)
    {
        return $this->userSubjects()
            ->byUser($userId)
            ->byDomain()
            ->first();
    }

    /**
     * Tạo Bản Ghi Mua Subject Cho User
     */
    public function createPurchaseForUser($userId, $amountPaid, $paymentMethod = null, $transactionCode = null)
    {
        return $this->userSubjects()->create([
            'user_id' => $userId,
            'amount_paid' => $amountPaid,
            'payment_method' => $paymentMethod,
            'transaction_code' => $transactionCode,
            'status' => 'completed',
            'purchased_at' => now(),
            'domain' => request()->getHost(),
        ]);
    }
}
