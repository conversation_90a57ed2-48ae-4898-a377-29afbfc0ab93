<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
            <h2 style="font-size: 1.25rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-chart-bar" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                <button onclick="exportData()"
                        style="background-color: #059669; color: #ffffff; padding: 8px 16px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                        onmouseover="this.style.backgroundColor='#047857'"
                        onmouseout="this.style.backgroundColor='#059669'">
                    <i class="fas fa-download" style="margin-right: 6px;"></i>
                    <span class="hidden-mobile">Xu<PERSON><PERSON></span>
                    <span class="show-mobile">Xuất</span>
                </button>
                <button onclick="refreshData()"
                        style="background-color: #6b7280; color: #ffffff; padding: 8px 16px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                        onmouseover="this.style.backgroundColor='#4b5563'"
                        onmouseout="this.style.backgroundColor='#6b7280'">
                    <i class="fas fa-sync-alt" style="margin-right: 6px;"></i>
                    <span class="hidden-mobile">Làm Mới</span>
                    <span class="show-mobile">Mới</span>
                </button>
            </div>
        </div>
    </x-slot>

    <!-- Mobile Responsive CSS -->
    <style>
        @media (max-width: 768px) {
            .hidden-mobile { display: none !important; }
            .show-mobile { display: inline !important; }
            .mobile-stack { flex-direction: column !important; }
            .mobile-full { width: 100% !important; }
            .mobile-text-sm { font-size: 0.75rem !important; }
            .mobile-p-sm { padding: 12px !important; }
            .mobile-gap-sm { gap: 8px !important; }
            .mobile-grid-1 { grid-template-columns: 1fr !important; }
            .mobile-grid-2 { grid-template-columns: repeat(2, 1fr) !important; }
            .mobile-overflow { overflow-x: auto !important; }
        }
        @media (min-width: 769px) {
            .hidden-mobile { display: inline !important; }
            .show-mobile { display: none !important; }
        }
    </style>

    <!-- Overview Stats Cards -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 32px;">
        <!-- Total Users -->
        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-users" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">+{{ $userRegistrations[29]['count'] ?? 0 }} Hôm Nay</span>
                </div>
                <h3 style="font-size: 2rem; font-weight: bold; margin: 0 0 8px 0;">{{ number_format($totalUsers) }}</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Tổng Người Dùng</p>
            </div>
        </div>

        <!-- Total Subjects -->
        <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-book" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">{{ $totalChapters }} Chương</span>
                </div>
                <h3 style="font-size: 2rem; font-weight: bold; margin: 0 0 8px 0;">{{ number_format($totalSubjects) }}</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Tổng Môn Học</p>
            </div>
        </div>

        <!-- Total Lessons -->
        <div style="background: linear-gradient(135deg, #d97706 0%, #b45309 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-video" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">{{ round($totalLessons / max($totalSubjects, 1), 1) }} Bài/Môn</span>
                </div>
                <h3 style="font-size: 2rem; font-weight: bold; margin: 0 0 8px 0;">{{ number_format($totalLessons) }}</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Tổng Bài Học</p>
            </div>
        </div>

        <!-- Admin Users -->
        <div style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-user-shield" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">{{ round($totalAdmins / max($totalUsers, 1) * 100, 1) }}% tổng</span>
                </div>
                <h3 style="font-size: 2rem; font-weight: bold; margin: 0 0 8px 0;">{{ number_format($totalAdmins) }}</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Quản Trị Viên</p>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 24px; margin-bottom: 32px;">
        <!-- User Registration Chart -->
        <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0;">
                    <i class="fas fa-chart-line" style="margin-right: 8px; color: #2563eb;"></i>
                    Đăng Ký Người Dùng (30 Ngày)
                </h3>
                <div style="display: flex; gap: 8px;">
                    <button onclick="changeChartType('users')" id="btn-users"
                            style="background-color: #2563eb; color: #ffffff; padding: 6px 12px; border-radius: 6px; border: none; font-size: 0.875rem; cursor: pointer;">
                        Người Dùng
                    </button>
                    <button onclick="changeChartType('content')" id="btn-content"
                            style="background-color: #f3f4f6; color: #374151; padding: 6px 12px; border-radius: 6px; border: none; font-size: 0.875rem; cursor: pointer;">
                        Nội Dung
                    </button>
                </div>
            </div>
            <canvas id="registrationChart" style="width: 100%; height: 300px;"></canvas>
        </div>

        <!-- Top Subjects -->
        <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 20px 0;">
                <i class="fas fa-trophy" style="margin-right: 8px; color: #d97706;"></i>
                Top Môn Học
            </h3>
            <div style="space-y: 16px;">
                @foreach($topSubjects as $index => $subject)
                    <div style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; background-color: #f8fafc; margin-bottom: 12px;">
                        <div style="width: 32px; height: 32px; background-color: {{ ['#2563eb', '#059669', '#d97706', '#dc2626', '#7c3aed'][$index] ?? '#6b7280' }}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 600; font-size: 0.875rem;">
                            {{ $index + 1 }}
                        </div>
                        <div style="flex: 1;">
                            <h4 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 0.875rem;">
                                {{ Str::limit($subject->title, 25) }}
                            </h4>
                            <div style="display: flex; gap: 12px; font-size: 0.75rem; color: #6b7280;">
                                <span><i class="fas fa-bookmark" style="margin-right: 4px;"></i>{{ $subject->chapters_count }} Chương</span>
                                <span><i class="fas fa-video" style="margin-right: 4px;"></i>{{ $subject->lessons_count }} Bài</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Monthly Statistics Table -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden; margin-bottom: 32px;">
        <div style="background-color: #f8fafc; padding: 20px 24px; border-bottom: 1px solid #e5e7eb;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0;">
                <i class="fas fa-calendar-alt" style="margin-right: 8px; color: #2563eb;"></i>
                Thống Kê Theo Tháng (12 Tháng Gần Nhất)
            </h3>
        </div>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f8fafc;">
                        <th style="padding: 16px 24px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                            <i class="fas fa-calendar" style="margin-right: 8px;"></i>Tháng
                        </th>
                        <th style="padding: 16px 24px; text-align: center; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                            <i class="fas fa-users" style="margin-right: 8px;"></i>Người Dùng Mới
                        </th>
                        <th style="padding: 16px 24px; text-align: center; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                            <i class="fas fa-book" style="margin-right: 8px;"></i>Môn Học Mới
                        </th>
                        <th style="padding: 16px 24px; text-align: center; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                            <i class="fas fa-bookmark" style="margin-right: 8px;"></i>Chương Mới
                        </th>
                        <th style="padding: 16px 24px; text-align: center; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                            <i class="fas fa-video" style="margin-right: 8px;"></i>Bài Học Mới
                        </th>
                        <th style="padding: 16px 24px; text-align: center; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                            <i class="fas fa-chart-line" style="margin-right: 8px;"></i>Tổng Hoạt Động
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($monthlyStats as $stat)
                        <tr style="border-bottom: 1px solid #f3f4f6;">
                            <td style="padding: 16px 24px; font-weight: 600; color: #000000;">
                                {{ $stat['month'] }}
                            </td>
                            <td style="padding: 16px 24px; text-align: center;">
                                <span style="background-color: #dbeafe; color: #2563eb; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $stat['users'] }}
                                </span>
                            </td>
                            <td style="padding: 16px 24px; text-align: center;">
                                <span style="background-color: #d1fae5; color: #059669; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $stat['subjects'] }}
                                </span>
                            </td>
                            <td style="padding: 16px 24px; text-align: center;">
                                <span style="background-color: #fef3c7; color: #d97706; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $stat['chapters'] }}
                                </span>
                            </td>
                            <td style="padding: 16px 24px; text-align: center;">
                                <span style="background-color: #fecaca; color: #dc2626; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $stat['lessons'] }}
                                </span>
                            </td>
                            <td style="padding: 16px 24px; text-align: center;">
                                <span style="background-color: #f3f4f6; color: #374151; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $stat['users'] + $stat['subjects'] + $stat['chapters'] + $stat['lessons'] }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px;">
        <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 20px 0;">
            <i class="fas fa-bolt" style="margin-right: 8px; color: #2563eb;"></i>
            Thao Tác Nhanh
        </h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
            <a href="{{ route('admin.users.index') }}"
               style="display: flex; align-items: center; gap: 12px; padding: 16px; background-color: #f8fafc; border-radius: 8px; text-decoration: none; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#f1f5f9'"
               onmouseout="this.style.backgroundColor='#f8fafc'">
                <div style="width: 40px; height: 40px; background-color: #2563eb; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-users" style="color: #ffffff; font-size: 18px;"></i>
                </div>
                <div>
                    <h4 style="font-weight: 600; color: #000000; margin: 0; font-size: 0.875rem;">Quản Lý Người Dùng</h4>
                    <p style="color: #6b7280; margin: 0; font-size: 0.75rem;">Xem và quản lý tài khoản</p>
                </div>
            </a>

            <a href="{{ route('admin.subjects.index') }}"
               style="display: flex; align-items: center; gap: 12px; padding: 16px; background-color: #f8fafc; border-radius: 8px; text-decoration: none; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#f1f5f9'"
               onmouseout="this.style.backgroundColor='#f8fafc'">
                <div style="width: 40px; height: 40px; background-color: #059669; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-book" style="color: #ffffff; font-size: 18px;"></i>
                </div>
                <div>
                    <h4 style="font-weight: 600; color: #000000; margin: 0; font-size: 0.875rem;">Quản Lý Môn Học</h4>
                    <p style="color: #6b7280; margin: 0; font-size: 0.75rem;">Tạo và chỉnh sửa môn học</p>
                </div>
            </a>

            <a href="{{ route('admin.lessons.index') }}"
               style="display: flex; align-items: center; gap: 12px; padding: 16px; background-color: #f8fafc; border-radius: 8px; text-decoration: none; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#f1f5f9'"
               onmouseout="this.style.backgroundColor='#f8fafc'">
                <div style="width: 40px; height: 40px; background-color: #d97706; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-video" style="color: #ffffff; font-size: 18px;"></i>
                </div>
                <div>
                    <h4 style="font-weight: 600; color: #000000; margin: 0; font-size: 0.875rem;">Quản Lý Bài Học</h4>
                    <p style="color: #6b7280; margin: 0; font-size: 0.75rem;">Thêm video và tài liệu</p>
                </div>
            </a>

            <button onclick="exportData()"
                    style="display: flex; align-items: center; gap: 12px; padding: 16px; background-color: #f8fafc; border-radius: 8px; border: none; cursor: pointer; transition: all 0.3s ease; width: 100%;"
                    onmouseover="this.style.backgroundColor='#f1f5f9'"
                    onmouseout="this.style.backgroundColor='#f8fafc'">
                <div style="width: 40px; height: 40px; background-color: #dc2626; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-download" style="color: #ffffff; font-size: 18px;"></i>
                </div>
                <div style="text-align: left;">
                    <h4 style="font-weight: 600; color: #000000; margin: 0; font-size: 0.875rem;">Xuất Báo Cáo</h4>
                    <p style="color: #6b7280; margin: 0; font-size: 0.75rem;">Tải về file Excel</p>
                </div>
            </button>
        </div>
    </div>

    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <!-- JavaScript -->
    <script>
        let chart;
        let currentChartType = 'users';

        // Initialize chart when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add a small delay to ensure DOM is fully loaded
            setTimeout(initChart, 100);
        });

        function initChart() {
            try {
                const canvas = document.getElementById('registrationChart');
                if (!canvas) {
                    console.error('Canvas element not found');
                    return;
                }

                const ctx = canvas.getContext('2d');

                // Destroy existing chart if it exists
                if (chart) {
                    chart.destroy();
                }

                const userData = @json($userRegistrations ?? []);
                const contentData = @json($contentCreationStats ?? []);

                // Validate data
                if (!userData || userData.length === 0) {
                    console.error('No user data available');
                    return;
                }

                chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: userData.map(item => item.date || ''),
                        datasets: [{
                            label: 'Người Dùng Mới',
                            data: userData.map(item => item.count || 0),
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#2563eb',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#2563eb',
                                borderWidth: 1
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: '#f3f4f6',
                                    drawBorder: false
                                },
                                ticks: {
                                    color: '#6b7280',
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    color: '#f3f4f6',
                                    drawBorder: false
                                },
                                ticks: {
                                    color: '#6b7280',
                                    font: {
                                        size: 12
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('Chart initialized successfully');
            } catch (error) {
                console.error('Error initializing chart:', error);
                // Show fallback message
                const canvas = document.getElementById('registrationChart');
                if (canvas) {
                    canvas.style.display = 'none';
                    const parent = canvas.parentElement;
                    const fallback = document.createElement('div');
                    fallback.style.cssText = 'display: flex; align-items: center; justify-content: center; height: 300px; background-color: #f8fafc; border-radius: 8px; color: #6b7280;';
                    fallback.innerHTML = '<div><i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 16px; display: block; text-align: center;"></i><p>Không thể tải biểu đồ</p></div>';
                    parent.appendChild(fallback);
                }
            }
        }

        function changeChartType(type) {
            if (!chart) {
                console.error('Chart not initialized');
                return;
            }

            try {
                currentChartType = type;

                // Update button styles
                const btnUsers = document.getElementById('btn-users');
                const btnContent = document.getElementById('btn-content');

                if (btnUsers && btnContent) {
                    btnUsers.style.backgroundColor = type === 'users' ? '#2563eb' : '#f3f4f6';
                    btnUsers.style.color = type === 'users' ? '#ffffff' : '#374151';
                    btnContent.style.backgroundColor = type === 'content' ? '#2563eb' : '#f3f4f6';
                    btnContent.style.color = type === 'content' ? '#ffffff' : '#374151';
                }

                const userData = @json($userRegistrations ?? []);
                const contentData = @json($contentCreationStats ?? []);

                if (type === 'users') {
                    chart.data.datasets = [{
                        label: 'Người Dùng Mới',
                        data: userData.map(item => item.count || 0),
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#2563eb',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }];

                    const titleElement = document.querySelector('#registrationChart').parentElement.querySelector('h3');
                    if (titleElement) {
                        titleElement.innerHTML = '<i class="fas fa-chart-line" style="margin-right: 8px; color: #2563eb;"></i>Đăng Ký Người Dùng (30 Ngày)';
                    }

                    chart.options.plugins.legend.display = false;
                } else {
                    chart.data.datasets = [
                        {
                            label: 'Môn Học',
                            data: contentData.map(item => item.subjects || 0),
                            borderColor: '#059669',
                            backgroundColor: 'rgba(5, 150, 105, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointBackgroundColor: '#059669',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4
                        },
                        {
                            label: 'Chương',
                            data: contentData.map(item => item.chapters || 0),
                            borderColor: '#d97706',
                            backgroundColor: 'rgba(217, 119, 6, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointBackgroundColor: '#d97706',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4
                        },
                        {
                            label: 'Bài Học',
                            data: contentData.map(item => item.lessons || 0),
                            borderColor: '#dc2626',
                            backgroundColor: 'rgba(220, 38, 38, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointBackgroundColor: '#dc2626',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4
                        }
                    ];

                    const titleElement = document.querySelector('#registrationChart').parentElement.querySelector('h3');
                    if (titleElement) {
                        titleElement.innerHTML = '<i class="fas fa-chart-line" style="margin-right: 8px; color: #2563eb;"></i>Tạo Nội Dung (30 Ngày)';
                    }

                    chart.options.plugins.legend.display = true;
                }

                chart.update('active');
                console.log('Chart updated to type:', type);
            } catch (error) {
                console.error('Error changing chart type:', error);
            }
        }

        function refreshData() {
            // Show loading indicator
            const refreshBtn = document.querySelector('button[onclick="refreshData()"]');
            if (refreshBtn) {
                const originalText = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 8px;"></i>Đang Tải...';
                refreshBtn.disabled = true;

                setTimeout(() => {
                    location.reload();
                }, 500);
            } else {
                location.reload();
            }
        }

        function exportData() {
            try {
                // Create CSV data with BOM for proper UTF-8 encoding
                const monthlyData = @json($monthlyStats ?? []);
                let csv = '\uFEFF'; // BOM for UTF-8
                csv += 'Tháng,Người Dùng Mới,Môn Học Mới,Chương Mới,Bài Học Mới,Tổng Hoạt Động\n';

                if (monthlyData && monthlyData.length > 0) {
                    monthlyData.forEach(stat => {
                        const total = (stat.users || 0) + (stat.subjects || 0) + (stat.chapters || 0) + (stat.lessons || 0);
                        csv += `${stat.month || ''},${stat.users || 0},${stat.subjects || 0},${stat.chapters || 0},${stat.lessons || 0},${total}\n`;
                    });
                } else {
                    csv += 'Không có dữ liệu,0,0,0,0,0\n';
                }

                // Add summary data
                csv += '\nTổng Kết Hệ Thống\n';
                csv += `Tổng Người Dùng,{{ $totalUsers ?? 0 }}\n`;
                csv += `Tổng Quản Trị Viên,{{ $totalAdmins ?? 0 }}\n`;
                csv += `Tổng Môn Học,{{ $totalSubjects ?? 0 }}\n`;
                csv += `Tổng Chương,{{ $totalChapters ?? 0 }}\n`;
                csv += `Tổng Bài Học,{{ $totalLessons ?? 0 }}\n`;
                csv += `Người Dùng Đã Xác Thực,{{ $verifiedUsers ?? 0 }}\n`;

                // Download CSV
                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `thong-ke-he-thong-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                const exportBtn = document.querySelector('button[onclick="exportData()"]');
                if (exportBtn) {
                    const originalText = exportBtn.innerHTML;
                    exportBtn.innerHTML = '<i class="fas fa-check" style="margin-right: 8px;"></i>Đã Tải Xuống';
                    exportBtn.style.backgroundColor = '#059669';

                    setTimeout(() => {
                        exportBtn.innerHTML = originalText;
                        exportBtn.style.backgroundColor = '#dc2626';
                    }, 2000);
                }

                console.log('Export completed successfully');
            } catch (error) {
                console.error('Error exporting data:', error);
                alert('Có lỗi xảy ra khi xuất dữ liệu. Vui lòng thử lại.');
            }
        }

        // Add error handling for missing elements
        window.addEventListener('error', function(e) {
            console.error('JavaScript error:', e.error);
        });

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js not loaded');
            document.addEventListener('DOMContentLoaded', function() {
                const canvas = document.getElementById('registrationChart');
                if (canvas) {
                    canvas.style.display = 'none';
                    const parent = canvas.parentElement;
                    const fallback = document.createElement('div');
                    fallback.style.cssText = 'display: flex; align-items: center; justify-content: center; height: 300px; background-color: #f8fafc; border-radius: 8px; color: #6b7280;';
                    fallback.innerHTML = '<div><i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px; display: block; text-align: center; color: #dc2626;"></i><p>Không thể tải thư viện biểu đồ</p></div>';
                    parent.appendChild(fallback);
                }
            });
        }
    </script>
</x-admin-layout>