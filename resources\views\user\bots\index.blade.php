<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-robot" style="margin-right: 8px; color: #2563eb;"></i>
                Bot Telegram & Discord
            </h2>
            <div style="display: flex; gap: 8px; align-items: center;">
                <a href="{{ route('user.bots.guide') }}"
                   style="background-color: #059669; color: #ffffff; padding: 8px 16px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem;"
                   onmouseover="this.style.backgroundColor='#047857'"
                   onmouseout="this.style.backgroundColor='#059669'">
                    <i class="fas fa-question-circle" style="margin-right: 6px;"></i>
                    Hướng Dẫn
                </a>
            </div>
        </div>
    </x-slot>

    <!-- Introduction Card -->
    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 24px; color: #ffffff;" class="mobile-p-sm">
            <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 12px 0;" class="mobile-text-sm">
                Tham Gia Nhóm Học Tập
            </h3>
            <p style="margin: 0; opacity: 0.9; line-height: 1.5;">
                Kết Nối Với Cộng Đồng Học Viên Qua Telegram Và Discord. Nhận Thông Báo Bài Học Mới, Tương Tác Với Bot Hỗ Trợ Và Trao Đổi Kiến Thức Cùng Bạn Bè.
            </p>
        </div>
        
        <div style="padding: 24px;" class="mobile-p-sm">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;" class="mobile-grid mobile-gap-sm">
                <!-- Telegram Features -->
                <div>
                    <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0; display: flex; align-items: center; gap: 8px;">
                        <i class="fab fa-telegram" style="color: #0088cc;"></i>
                        Telegram Bot
                    </h4>
                    <ul style="margin: 0; padding-left: 20px; color: #374151; line-height: 1.6;">
                        <li>/bai - Xem Danh Sách Bài Học Mới Nhất</li>
                        <li>/tai [ID] - Tải Tài Liệu Bài Học</li>
                        <li>/ho_tro - Liên Hệ Hỗ Trợ</li>
                        <li>Nhận Thông Báo Bài Học Mới Tự Động</li>
                        <li>Nhắc Nhở Học Tập Hàng Ngày</li>
                    </ul>
                </div>

                <!-- Discord Features -->
                <div>
                    <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0; display: flex; align-items: center; gap: 8px;">
                        <i class="fab fa-discord" style="color: #7289da;"></i>
                        Discord Bot
                    </h4>
                    <ul style="margin: 0; padding-left: 20px; color: #374151; line-height: 1.6;">
                        <li>Thông Báo Bài Học Mới Với Rich Embed</li>
                        <li>Thông Báo Hệ Thống Và Bảo Trì</li>
                        <li>Báo Cáo Thống Kê Học Tập Hàng Tuần</li>
                        <li>Tương Tác Cộng Đồng Học Viên</li>
                        <li>Chia Sẻ Tài Liệu Và Kiến Thức</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    @if($subjects->count() > 0)
        <!-- Subject Groups -->
        <div style="display: grid; gap: 24px;" class="mobile-gap-sm">
            @foreach($subjects as $subject)
                <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
                    <!-- Subject Header -->
                    <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); padding: 20px; color: #ffffff;" class="mobile-p-sm">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;">
                            <div style="flex: 1;">
                                <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 8px 0;" class="mobile-text-sm">
                                    {{ $subject->title }}
                                </h3>
                                <div style="display: flex; gap: 16px; align-items: center; flex-wrap: wrap;" class="mobile-stack mobile-gap-sm">
                                    <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                        {{ $subject->chapters->count() }} Chương
                                    </span>
                                    <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                        {{ $subject->chapters->sum(function($chapter) { return $chapter->lessons->count(); }) }} Bài Học
                                    </span>
                                    @if($subject->instructor_name)
                                        <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                            {{ $subject->instructor_name }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Group Links -->
                    <div style="padding: 24px;" class="mobile-p-sm">
                        @if($subject->description)
                            <p style="color: #6b7280; margin: 0 0 20px 0; line-height: 1.5;">
                                {{ $subject->description }}
                            </p>
                        @endif

                        <div style="display: grid; gap: 16px;">
                            @if($subject->group_link)
                                <div style="padding: 16px; background-color: #f8fafc; border-radius: 12px; border: 1px solid #e5e7eb;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                                        <div style="flex: 1;">
                                            <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 4px 0;">
                                                <i class="fas fa-users" style="margin-right: 8px; color: #2563eb;"></i>
                                                Nhóm Học Chính
                                            </h4>
                                            <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">
                                                Tham Gia Nhóm Để Thảo Luận Và Học Tập Cùng Nhau
                                            </p>
                                        </div>
                                        <a href="{{ $subject->group_link }}" 
                                           target="_blank"
                                           style="background-color: #2563eb; color: #ffffff; padding: 10px 20px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem;"
                                           onmouseover="this.style.backgroundColor='#1d4ed8'"
                                           onmouseout="this.style.backgroundColor='#2563eb'">
                                            <i class="fas fa-external-link-alt" style="margin-right: 6px;"></i>
                                            Tham Gia Nhóm
                                        </a>
                                    </div>
                                </div>
                            @endif

                            @if($subject->telegram_group_id)
                                <div style="padding: 16px; background-color: #f0f9ff; border-radius: 12px; border: 1px solid #0ea5e9;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                                        <div style="flex: 1;">
                                            <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 4px 0;">
                                                <i class="fab fa-telegram" style="margin-right: 8px; color: #0088cc;"></i>
                                                Telegram Bot
                                            </h4>
                                            <p style="color: #0369a1; margin: 0; font-size: 0.875rem;">
                                                Bot Hỗ Trợ Học Tập Với Các Lệnh Thông Minh
                                            </p>
                                        </div>
                                        <div style="display: flex; gap: 8px;">
                                            <span style="background-color: #0ea5e9; color: #ffffff; padding: 6px 12px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                                Bot Hoạt Động
                                            </span>
                                        </div>
                                    </div>
                                    <div style="margin-top: 12px; padding: 12px; background-color: #ffffff; border-radius: 8px; border: 1px solid #bae6fd;">
                                        <p style="margin: 0 0 8px 0; font-weight: 600; color: #0369a1; font-size: 0.875rem;">
                                            Các Lệnh Bot:
                                        </p>
                                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; font-size: 0.75rem; color: #0369a1;">
                                            <span><code>/bai</code> - Bài Học Mới</span>
                                            <span><code>/tai [ID]</code> - Tải Tài Liệu</span>
                                            <span><code>/ho_tro</code> - Hỗ Trợ</span>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($subject->discord_webhook_url)
                                <div style="padding: 16px; background-color: #faf5ff; border-radius: 12px; border: 1px solid #a855f7;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                                        <div style="flex: 1;">
                                            <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 4px 0;">
                                                <i class="fab fa-discord" style="margin-right: 8px; color: #7289da;"></i>
                                                Discord Bot
                                            </h4>
                                            <p style="color: #7c3aed; margin: 0; font-size: 0.875rem;">
                                                Nhận Thông Báo Và Thống Kê Học Tập
                                            </p>
                                        </div>
                                        <div style="display: flex; gap: 8px;">
                                            <span style="background-color: #a855f7; color: #ffffff; padding: 6px 12px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                                Bot Hoạt Động
                                            </span>
                                        </div>
                                    </div>
                                    <div style="margin-top: 12px; padding: 12px; background-color: #ffffff; border-radius: 8px; border: 1px solid #d8b4fe;">
                                        <p style="margin: 0 0 8px 0; font-weight: 600; color: #7c3aed; font-size: 0.875rem;">
                                            Tính Năng Bot:
                                        </p>
                                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; font-size: 0.75rem; color: #7c3aed;">
                                            <span>Thông Báo Bài Học Mới</span>
                                            <span>Báo Cáo Thống Kê Tuần</span>
                                            <span>Thông Báo Hệ Thống</span>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Subject Actions -->
                        <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb; display: flex; gap: 12px; flex-wrap: wrap;" class="mobile-stack mobile-gap-sm">
                            <a href="{{ route('user.subjects.show', $subject) }}" 
                               style="background-color: #2563eb; color: #ffffff; padding: 10px 20px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem;"
                               onmouseover="this.style.backgroundColor='#1d4ed8'"
                               onmouseout="this.style.backgroundColor='#2563eb'">
                                <i class="fas fa-book-open" style="margin-right: 6px;"></i>
                                Xem Bài Học
                            </a>
                            
                            @if($subject->chapters->sum(function($chapter) { return $chapter->lessons->where('file_path', '!=', null)->count(); }) > 0)
                                <a href="{{ route('user.documents.index') }}" 
                                   style="background-color: #059669; color: #ffffff; padding: 10px 20px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem;"
                                   onmouseover="this.style.backgroundColor='#047857'"
                                   onmouseout="this.style.backgroundColor='#059669'">
                                    <i class="fas fa-download" style="margin-right: 6px;"></i>
                                    Tài Liệu
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div style="text-align: center; padding: 80px 20px; background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
                <i class="fas fa-robot" style="font-size: 48px; color: #9ca3af;"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin: 0 0 12px 0;">
                Chưa Có Bot Nào Hoạt Động
            </h3>
            <p style="color: #6b7280; margin: 0 0 32px 0; font-size: 1rem; max-width: 400px; margin-left: auto; margin-right: auto;">
                Hiện Tại Chưa Có Môn Học Nào Được Cấu Hình Bot. Vui Lòng Liên Hệ Admin Để Được Hỗ Trợ.
            </p>
            <div style="display: flex; gap: 16px; justify-content: center; flex-wrap: wrap;">
                <a href="{{ route('user.subjects.index') }}" 
                   style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'"
                   onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-book" style="margin-right: 8px;"></i>
                    Xem Môn Học
                </a>
                <a href="{{ route('user.bots.guide') }}" 
                   style="background-color: #f3f4f6; color: #374151; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                   onmouseout="this.style.backgroundColor='#f3f4f6'">
                    <i class="fas fa-question-circle" style="margin-right: 8px;"></i>
                    Hướng Dẫn
                </a>
            </div>
        </div>
    @endif
</x-app-layout>
