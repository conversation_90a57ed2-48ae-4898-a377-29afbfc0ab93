<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\Chapter;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LessonController extends Controller
{
    /**
     * Hiển <PERSON>ch <PERSON>
     */
    public function index()
    {
        $lessons = Lesson::with(['chapter.subject'])
            ->orderBy('chapter_id')
            ->orderBy('order')
            ->get();

        return view('admin.lessons.index', compact('lessons'));
    }

    /**
     * Hiển Thị Form Tạo Bài H<PERSON>
     */
    public function create()
    {
        $subjects = Subject::with('chapters')->orderBy('title')->get();
        return view('admin.lessons.create', compact('subjects'));
    }

    /**
     * Lưu Bài H<PERSON> Mới
     */
    public function store(Request $request)
    {
        $request->validate([
            'chapter_id' => 'required|exists:chapters,id',
            'title' => 'required|string|max:255',
            'youtube_link' => 'nullable|url',
            'file_path' => 'nullable|file|mimes:pdf,doc,docx,ppt,pptx,zip,rar|max:10240',
            'order' => 'nullable|integer|min:0',
        ]);

        $data = $request->only(['chapter_id', 'title', 'youtube_link', 'order']);

        // Xử Lý Upload File
        if ($request->hasFile('file_path')) {
            $data['file_path'] = $request->file('file_path')->store('lessons', 'public');
        }

        // Nếu không có order, tự động tăng
        if (!$data['order']) {
            $data['order'] = Lesson::where('chapter_id', $data['chapter_id'])->max('order') + 1;
        }

        Lesson::create($data);

        return redirect()->route('admin.lessons.index')
            ->with('success', 'Bài Học Đã Được Tạo Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Bài Học
     */
    public function show(Lesson $lesson)
    {
        $lesson->load(['chapter.subject']);
        return view('admin.lessons.show', compact('lesson'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa Bài Học
     */
    public function edit(Lesson $lesson)
    {
        $subjects = Subject::with('chapters')->orderBy('title')->get();
        return view('admin.lessons.edit', compact('lesson', 'subjects'));
    }

    /**
     * Cập Nhật Bài Học
     */
    public function update(Request $request, Lesson $lesson)
    {
        $request->validate([
            'chapter_id' => 'required|exists:chapters,id',
            'title' => 'required|string|max:255',
            'youtube_link' => 'nullable|url',
            'file_path' => 'nullable|file|mimes:pdf,doc,docx,ppt,pptx,zip,rar|max:10240',
            'order' => 'nullable|integer|min:0',
        ]);

        $data = $request->only(['chapter_id', 'title', 'youtube_link', 'order']);

        // Xử Lý Upload File Mới
        if ($request->hasFile('file_path')) {
            // Xóa File Cũ
            if ($lesson->file_path) {
                Storage::disk('public')->delete($lesson->file_path);
            }
            $data['file_path'] = $request->file('file_path')->store('lessons', 'public');
        }

        // Nếu không có order, giữ nguyên order hiện tại
        if (!$data['order']) {
            $data['order'] = $lesson->order;
        }

        $lesson->update($data);

        return redirect()->route('admin.lessons.index')
            ->with('success', 'Bài Học Đã Được Cập Nhật Thành Công!');
    }

    /**
     * Xóa Bài Học
     */
    public function destroy(Lesson $lesson)
    {
        // Xóa File
        if ($lesson->file_path) {
            Storage::disk('public')->delete($lesson->file_path);
        }

        $lesson->delete();

        return redirect()->route('admin.lessons.index')
            ->with('success', 'Bài Học Đã Được Xóa Thành Công!');
    }
}
