<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Models\Chapter;
use App\Models\Lesson;
use Illuminate\Http\Request;

class SubjectController extends Controller
{
    /**
     * Hiển Thị <PERSON>h Sách Môn <PERSON> Cho User
     */
    public function index()
    {
        $subjects = Subject::with(['chapters.lessons'])
            ->withCount(['chapters', 'lessons'])
            ->orderBy('title')
            ->get();

        return view('user.subjects.index', compact('subjects'));
    }

    /**
     * Hiển Thị Chi Tiết Môn <PERSON>
     */
    public function show(Subject $subject)
    {
        $subject->load(['chapters' => function($query) {
            $query->orderBy('order')->with(['lessons' => function($query) {
                $query->orderBy('order');
            }]);
        }]);

        // Kiểm tra quyền truy cập của user
        $hasAccess = $subject->canUserAccess(auth()->id());
        $purchaseInfo = null;

        if (auth()->check() && !$subject->isFree()) {
            $purchaseInfo = $subject->getUserPurchaseInfo(auth()->id());
        }

        return view('user.subjects.show', compact('subject', 'hasAccess', 'purchaseInfo'));
    }

    /**
     * Hiển Thị Bài Học
     */
    public function lesson(Subject $subject, Chapter $chapter, Lesson $lesson)
    {
        // Kiểm tra lesson có thuộc chapter và subject không
        if ($lesson->chapter_id !== $chapter->id || $chapter->subject_id !== $subject->id) {
            abort(404);
        }

        $lesson->load([
            'chapter.subject',
            'comments' => function($query) {
                $query->parentComments()->with('user', 'replies.user')->latest();
            }
        ]);

        // Lấy bài học trước và sau
        $previousLesson = Lesson::where('chapter_id', $chapter->id)
            ->where('order', '<', $lesson->order)
            ->orderBy('order', 'desc')
            ->first();

        $nextLesson = Lesson::where('chapter_id', $chapter->id)
            ->where('order', '>', $lesson->order)
            ->orderBy('order', 'asc')
            ->first();

        // Lấy progress của user hiện tại
        $userProgress = $lesson->getProgressForUser(auth()->id());
        $isCompleted = $lesson->isCompletedByUser(auth()->id());

        return view('user.subjects.lesson', compact(
            'subject',
            'chapter',
            'lesson',
            'previousLesson',
            'nextLesson',
            'userProgress',
            'isCompleted'
        ));
    }
}
