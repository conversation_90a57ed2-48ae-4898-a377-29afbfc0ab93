<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('balance', 15, 2)->default(0)->after('role')->comment('Số Dư Tà<PERSON>');
            $table->decimal('total_deposit', 15, 2)->default(0)->after('balance')->comment('Tổng Số Tiền Đã Nạp');
            $table->decimal('total_spent', 15, 2)->default(0)->after('total_deposit')->comment('Tổng Số Tiền Đã Chi');
            $table->string('ref_code')->nullable()->after('total_spent')->comment('Mã Giới Thiệu Của User');
            $table->string('ref_by')->nullable()->after('ref_code')->comment('Được <PERSON>ới Thiệu Bởi');
            $table->string('domain')->nullable()->after('ref_by')->comment('Tên Miền');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'balance',
                'total_deposit',
                'total_spent',
                'ref_code',
                'ref_by',
                'domain'
            ]);
        });
    }
};
