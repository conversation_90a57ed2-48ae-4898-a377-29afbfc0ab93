<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-robot" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON>n <PERSON> Telegram & Discord
            </h2>
            <div style="display: flex; gap: 8px; align-items: center;">
                <span style="background-color: #dbeafe; color: #2563eb; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                    <i class="fas fa-comments" style="margin-right: 4px;"></i>
                    {{ $subjects->count() }} M<PERSON>n <PERSON>
                </span>
            </div>
        </div>
    </x-slot>

    @if(session('success'))
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; color: #15803d; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div style="background-color: #fef2f2; border: 1px solid #dc2626; color: #dc2626; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
            <i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>
            {{ session('error') }}
        </div>
    @endif

    @if(session('warning'))
        <div style="background-color: #fef3c7; border: 1px solid #d97706; color: #92400e; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
            <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
            {{ session('warning') }}
        </div>
    @endif

    <!-- Bot Configuration Cards -->
    <div style="display: grid; gap: 24px;">
        @foreach($subjects as $subject)
            <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
                <!-- Subject Header -->
                <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 24px; color: #ffffff;" class="mobile-p-sm">
                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;">
                        <div style="flex: 1;">
                            <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 8px 0;" class="mobile-text-sm">
                                {{ $subject->title }}
                            </h3>
                            <div style="display: flex; gap: 16px; align-items: center; flex-wrap: wrap;" class="mobile-stack mobile-gap-sm">
                                <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                    {{ $subject->chapters->count() }} Chương
                                </span>
                                <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                    {{ $subject->chapters->sum(function($chapter) { return $chapter->lessons->count(); }) }} Bài Học
                                </span>
                                @if($subject->instructor_name)
                                    <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                        {{ $subject->instructor_name }}
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            @if($subject->telegram_group_id)
                                <span style="background-color: #059669; padding: 6px 12px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                    <i class="fab fa-telegram" style="margin-right: 4px;"></i>
                                    Telegram
                                </span>
                            @endif
                            @if($subject->discord_webhook_url)
                                <span style="background-color: #7c3aed; padding: 6px 12px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                    <i class="fab fa-discord" style="margin-right: 4px;"></i>
                                    Discord
                                </span>
                            @endif
                            @if($subject->auto_notify)
                                <span style="background-color: #16a34a; padding: 6px 12px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                    <i class="fas fa-bell" style="margin-right: 4px;"></i>
                                    Auto
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Bot Configuration -->
                <div style="padding: 24px;" class="mobile-p-sm">
                    <form method="POST" action="{{ route('admin.bots.update', $subject) }}">
                        @csrf
                        @method('PUT')

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;" class="mobile-grid mobile-gap-sm">
                            <!-- Telegram Configuration -->
                            <div>
                                <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0; display: flex; align-items: center; gap: 8px;">
                                    <i class="fab fa-telegram" style="color: #0088cc;"></i>
                                    Telegram Bot
                                </h4>
                                
                                <div style="margin-bottom: 16px;">
                                    <label for="telegram_group_id_{{ $subject->id }}" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                        Group Chat ID
                                    </label>
                                    <input type="text"
                                           id="telegram_group_id_{{ $subject->id }}"
                                           name="telegram_group_id"
                                           value="{{ old('telegram_group_id', $subject->telegram_group_id) }}"
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease; background-color: #ffffff;"
                                           onfocus="this.style.borderColor='#0088cc'"
                                           onblur="this.style.borderColor='#e5e7eb'"
                                           placeholder="-1001234567890">
                                    <div style="margin-top: 8px; color: #6b7280; font-size: 0.75rem;">
                                        ID Nhóm Telegram (Bắt Đầu Bằng -100)
                                    </div>
                                </div>

                                @if($subject->telegram_group_id)
                                    <button type="button" onclick="testTelegram({{ $subject->id }})"
                                            style="background-color: #0088cc; color: #ffffff; padding: 8px 16px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem; width: 100%;"
                                            onmouseover="this.style.backgroundColor='#006699'"
                                            onmouseout="this.style.backgroundColor='#0088cc'">
                                        <i class="fas fa-paper-plane" style="margin-right: 6px;"></i>
                                        Test Telegram
                                    </button>
                                @endif
                            </div>

                            <!-- Discord Configuration -->
                            <div>
                                <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0; display: flex; align-items: center; gap: 8px;">
                                    <i class="fab fa-discord" style="color: #7289da;"></i>
                                    Discord Bot
                                </h4>
                                
                                <div style="margin-bottom: 16px;">
                                    <label for="discord_webhook_url_{{ $subject->id }}" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                        Webhook URL
                                    </label>
                                    <input type="url"
                                           id="discord_webhook_url_{{ $subject->id }}"
                                           name="discord_webhook_url"
                                           value="{{ old('discord_webhook_url', $subject->discord_webhook_url) }}"
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease; background-color: #ffffff;"
                                           onfocus="this.style.borderColor='#7289da'"
                                           onblur="this.style.borderColor='#e5e7eb'"
                                           placeholder="https://discord.com/api/webhooks/...">
                                    <div style="margin-top: 8px; color: #6b7280; font-size: 0.75rem;">
                                        URL Webhook Discord Channel
                                    </div>
                                </div>

                                @if($subject->discord_webhook_url)
                                    <button type="button" onclick="testDiscord({{ $subject->id }})"
                                            style="background-color: #7289da; color: #ffffff; padding: 8px 16px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem; width: 100%;"
                                            onmouseover="this.style.backgroundColor='#5b6eae'"
                                            onmouseout="this.style.backgroundColor='#7289da'">
                                        <i class="fas fa-paper-plane" style="margin-right: 6px;"></i>
                                        Test Discord
                                    </button>
                                @endif
                            </div>
                        </div>

                        <!-- Auto Notification Setting -->
                        <div style="margin-bottom: 24px; padding: 16px; background-color: #f8fafc; border-radius: 8px; border: 1px solid #e5e7eb;">
                            <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                <input type="hidden" name="auto_notify" value="0">
                                <input type="checkbox"
                                       name="auto_notify"
                                       value="1"
                                       {{ $subject->auto_notify ? 'checked' : '' }}
                                       style="width: 20px; height: 20px; accent-color: #2563eb;">
                                <div>
                                    <span style="font-weight: 600; color: #000000; font-size: 0.875rem;">
                                        Tự Động Thông Báo Bài Học Mới
                                    </span>
                                    <div style="color: #6b7280; font-size: 0.75rem; margin-top: 2px;">
                                        Bot Sẽ Tự Động Gửi Thông Báo Khi Có Bài Học Mới
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- Action Buttons -->
                        <div style="display: flex; gap: 12px; flex-wrap: wrap;" class="mobile-stack mobile-gap-sm">
                            <button type="submit"
                                    style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                                    onmouseover="this.style.backgroundColor='#1d4ed8'"
                                    onmouseout="this.style.backgroundColor='#2563eb'">
                                <i class="fas fa-save" style="margin-right: 8px;"></i>
                                Lưu Cài Đặt
                            </button>

                            @if($subject->telegram_group_id || $subject->discord_webhook_url)
                                <button type="button" onclick="showManualNotification({{ $subject->id }})"
                                        style="background-color: #059669; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                                        onmouseover="this.style.backgroundColor='#047857'"
                                        onmouseout="this.style.backgroundColor='#059669'">
                                    <i class="fas fa-bullhorn" style="margin-right: 8px;"></i>
                                    Gửi Thông Báo
                                </button>

                                <button type="button" onclick="sendWeeklyStats({{ $subject->id }})"
                                        style="background-color: #7c3aed; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                                        onmouseover="this.style.backgroundColor='#6d28d9'"
                                        onmouseout="this.style.backgroundColor='#7c3aed'">
                                    <i class="fas fa-chart-bar" style="margin-right: 8px;"></i>
                                    Gửi Thống Kê
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Manual Notification Modal -->
    <div id="manualNotificationModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center;">
        <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; max-width: 500px; width: 90%; max-height: 90%; overflow-y: auto;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 20px 0;">
                Gửi Thông Báo Thủ Công
            </h3>
            
            <form id="manualNotificationForm" method="POST">
                @csrf
                
                <div style="margin-bottom: 20px;">
                    <label for="notification_message" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        Nội Dung Thông Báo
                    </label>
                    <textarea id="notification_message"
                              name="message"
                              rows="4"
                              required
                              style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease; background-color: #ffffff; resize: vertical;"
                              onfocus="this.style.borderColor='#2563eb'"
                              onblur="this.style.borderColor='#e5e7eb'"
                              placeholder="Nhập nội dung thông báo..."></textarea>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        Nền Tảng
                    </label>
                    <div style="display: flex; gap: 12px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="platform" value="telegram" style="accent-color: #2563eb;">
                            <span>Telegram</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="platform" value="discord" style="accent-color: #2563eb;">
                            <span>Discord</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="platform" value="both" checked style="accent-color: #2563eb;">
                            <span>Cả Hai</span>
                        </label>
                    </div>
                </div>

                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button type="button" onclick="closeManualNotification()"
                            style="background-color: #6b7280; color: #ffffff; padding: 10px 20px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;">
                        Hủy
                    </button>
                    <button type="submit"
                            style="background-color: #2563eb; color: #ffffff; padding: 10px 20px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;">
                        <i class="fas fa-paper-plane" style="margin-right: 6px;"></i>
                        Gửi Thông Báo
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        let currentSubjectId = null;

        function testTelegram(subjectId) {
            if (confirm('Bạn Có Muốn Test Telegram Bot?')) {
                window.location.href = `/admin/bots/${subjectId}/test-telegram`;
            }
        }

        function testDiscord(subjectId) {
            if (confirm('Bạn Có Muốn Test Discord Bot?')) {
                window.location.href = `/admin/bots/${subjectId}/test-discord`;
            }
        }

        function showManualNotification(subjectId) {
            currentSubjectId = subjectId;
            document.getElementById('manualNotificationForm').action = `/admin/bots/${subjectId}/send-notification`;
            document.getElementById('manualNotificationModal').style.display = 'flex';
        }

        function closeManualNotification() {
            document.getElementById('manualNotificationModal').style.display = 'none';
            document.getElementById('notification_message').value = '';
        }

        function sendWeeklyStats(subjectId) {
            if (confirm('Bạn Có Muốn Gửi Thống Kê Tuần Này?')) {
                window.location.href = `/admin/bots/${subjectId}/send-weekly-stats`;
            }
        }

        // Close modal when clicking outside
        document.getElementById('manualNotificationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeManualNotification();
            }
        });
    </script>
</x-admin-layout>
