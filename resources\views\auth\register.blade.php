<x-guest-layout>
    <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 24px; text-align: center; color: #000000;">
        <PERSON><PERSON><PERSON>
    </h2>

    <form method="POST" action="{{ route('register') }}">
        @csrf

        <!-- Name -->
        <div style="margin-bottom: 20px;">
            <label for="name" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000;">
                Họ V<PERSON> Tên
            </label>
            <input id="name" type="text" name="name" value="{{ old('name') }}" required autofocus autocomplete="name"
                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                   placeholder="Nhập H<PERSON> Tên">
            @error('name')
                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">{{ $message }}</div>
            @enderror
        </div>

        <!-- Email Address -->
        <div style="margin-bottom: 20px;">
            <label for="email" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000;">
                Email
            </label>
            <input id="email" type="email" name="email" value="{{ old('email') }}" required autocomplete="username"
                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                   placeholder="Nhập Email Của Bạn">
            @error('email')
                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">{{ $message }}</div>
            @enderror
        </div>

        <!-- Password -->
        <div style="margin-bottom: 20px;">
            <label for="password" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000;">
                Mật Khẩu
            </label>
            <input id="password" type="password" name="password" required autocomplete="new-password"
                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                   placeholder="Nhập Mật Khẩu">
            @error('password')
                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">{{ $message }}</div>
            @enderror
        </div>

        <!-- Confirm Password -->
        <div style="margin-bottom: 24px;">
            <label for="password_confirmation" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000;">
                Xác Nhận Mật Khẩu
            </label>
            <input id="password_confirmation" type="password" name="password_confirmation" required autocomplete="new-password"
                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                   placeholder="Nhập Lại Mật Khẩu">
            @error('password_confirmation')
                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">{{ $message }}</div>
            @enderror
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
            <a href="{{ route('login') }}"
               style="font-size: 14px; color: #2563eb; text-decoration: none; transition: color 0.3s ease;"
               onmouseover="this.style.color='#1d4ed8'"
               onmouseout="this.style.color='#2563eb'">
                Đã Có Tài Khoản?
            </a>

            <button type="submit"
                    style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease;"
                    onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(37, 99, 235, 0.3)'"
                    onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                Đăng Ký
            </button>
        </div>
    </form>
</x-guest-layout>
