<x-app-layout>
    <x-slot name="header">
        <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
            Dashboard
        </h2>
    </x-slot>

    <!-- Welcome Section -->
    <div style="background-color: #ffffff; border-radius: 12px; padding: 32px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; margin-bottom: 32px;">
        <h3 style="font-size: 2rem; font-weight: bold; color: #000000; margin: 0 0 16px 0;">
            <PERSON><PERSON><PERSON> Trở Lại, {{ auth()->user()->name }}! <i class="fas fa-hand-wave" style="color: #f59e0b;"></i>
        </h3>
        <p style="color: #6b7280; font-size: 1.1rem; margin: 0 0 24px 0;">
            Hôm <PERSON><PERSON>? H<PERSON><PERSON><PERSON>p <PERSON>.
        </p>
        <div style="display: flex; align-items: center; gap: 16px;">
            <span style="padding: 8px 16px; background-color: #dbeafe; color: #2563eb; border-radius: 20px; font-size: 0.875rem; font-weight: 600;">
                @if(auth()->user()->role === 'admin')
                    <i class="fas fa-user-cog" style="margin-right: 4px;"></i> Quản Trị Viên
                @else
                    <i class="fas fa-graduation-cap" style="margin-right: 4px;"></i> Học Viên
                @endif
            </span>
            <span style="color: #6b7280; font-size: 0.875rem;">
                <i class="fas fa-calendar-alt" style="margin-right: 4px;"></i>
                Thành Viên Từ {{ auth()->user()->created_at->format('d/m/Y') }}
            </span>
        </div>
    </div>

    <!-- Stats Cards -->
    <div style="display: grid; gap: 24px; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); margin-bottom: 32px;">
        <!-- Môn Học -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                <div>
                    <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                        Môn Học Đã Đăng Ký
                    </h4>
                    <p style="font-size: 2rem; font-weight: bold; color: #2563eb; margin: 0;">
                        0
                    </p>
                </div>
                <div style="width: 48px; height: 48px; background-color: #dbeafe; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-book" style="font-size: 20px; color: #2563eb;"></i>
                </div>
            </div>
            <a href="#" style="color: #2563eb; text-decoration: none; font-weight: 500; font-size: 0.875rem;">
                <i class="fas fa-arrow-right" style="margin-left: 4px;"></i> Xem Tất Cả
            </a>
        </div>

        <!-- Bài Học Hoàn Thành -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                <div>
                    <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                        Bài Học Hoàn Thành
                    </h4>
                    <p style="font-size: 2rem; font-weight: bold; color: #059669; margin: 0;">
                        0
                    </p>
                </div>
                <div style="width: 48px; height: 48px; background-color: #d1fae5; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-check-circle" style="font-size: 20px; color: #059669;"></i>
                </div>
            </div>
            <a href="#" style="color: #059669; text-decoration: none; font-weight: 500; font-size: 0.875rem;">
                <i class="fas fa-arrow-right" style="margin-left: 4px;"></i> Xem Chi Tiết
            </a>
        </div>

        <!-- Thời Gian Học -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                <div>
                    <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                        Thời Gian Học Tuần Này
                    </h4>
                    <p style="font-size: 2rem; font-weight: bold; color: #7c3aed; margin: 0;">
                        0h
                    </p>
                </div>
                <div style="width: 48px; height: 48px; background-color: #ede9fe; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-clock" style="font-size: 20px; color: #7c3aed;"></i>
                </div>
            </div>
            <a href="#" style="color: #7c3aed; text-decoration: none; font-weight: 500; font-size: 0.875rem;">
                <i class="fas fa-arrow-right" style="margin-left: 4px;"></i> Xem Lịch Sử
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div style="background-color: #ffffff; border-radius: 12px; padding: 32px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; margin-bottom: 32px;">
        <h3 style="font-size: 1.25rem; font-weight: bold; color: #000000; margin: 0 0 24px 0;">
            <i class="fas fa-history" style="margin-right: 8px; color: #6b7280;"></i>
            Hoạt Động Gần Đây
        </h3>
        <div style="text-align: center; padding: 40px 0;">
            <i class="fas fa-book-open" style="font-size: 48px; color: #d1d5db; margin-bottom: 16px; display: block;"></i>
            <p style="color: #6b7280; margin: 0;">
                Chưa Có Hoạt Động Nào. Hãy Bắt Đầu Học Ngay!
            </p>
        </div>
    </div>

    <!-- Quick Actions -->
    <div style="background-color: #ffffff; border-radius: 12px; padding: 32px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
        <h3 style="font-size: 1.25rem; font-weight: bold; color: #000000; margin: 0 0 24px 0;">
            <i class="fas fa-bolt" style="margin-right: 8px; color: #f59e0b;"></i>
            Thao Tác Nhanh
        </h3>
        <div style="display: grid; gap: 16px; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
            <a href="#"
               style="background-color: #2563eb; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
               onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-2px)'"
               onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                <i class="fas fa-book" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                Duyệt Môn Học
            </a>
            <a href="#"
               style="background-color: #059669; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
               onmouseover="this.style.backgroundColor='#047857'; this.style.transform='translateY(-2px)'"
               onmouseout="this.style.backgroundColor='#059669'; this.style.transform='translateY(0)'">
                <i class="fas fa-chart-line" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                Xem Tiến Độ
            </a>
            <a href="#"
               style="background-color: #7c3aed; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
               onmouseover="this.style.backgroundColor='#6d28d9'; this.style.transform='translateY(-2px)'"
               onmouseout="this.style.backgroundColor='#7c3aed'; this.style.transform='translateY(0)'">
                <i class="fas fa-download" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                Tải Tài Liệu
            </a>
            <a href="{{ route('profile.edit') }}"
               style="background-color: #dc2626; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
               onmouseover="this.style.backgroundColor='#b91c1c'; this.style.transform='translateY(-2px)'"
               onmouseout="this.style.backgroundColor='#dc2626'; this.style.transform='translateY(0)'">
                <i class="fas fa-cog" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                Cài Đặt
            </a>
        </div>
    </div>
</x-app-layout>
