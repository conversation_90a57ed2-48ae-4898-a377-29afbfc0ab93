<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-question-circle" style="margin-right: 8px; color: #2563eb;"></i>
                Hướng Dẫn <PERSON>ử <PERSON>
            </h2>
            <div style="display: flex; gap: 8px; align-items: center;">
                <a href="{{ route('user.bots.index') }}"
                   style="background-color: #6b7280; color: #ffffff; padding: 8px 16px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem;"
                   onmouseover="this.style.backgroundColor='#4b5563'"
                   onmouseout="this.style.backgroundColor='#6b7280'">
                    <i class="fas fa-arrow-left" style="margin-right: 6px;"></i>
                    Quay Lại
                </a>
            </div>
        </div>
    </x-slot>

    <!-- Telegram Guide -->
    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #0088cc 0%, #006699 100%); padding: 24px; color: #ffffff;" class="mobile-p-sm">
            <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 12px 0;" class="mobile-text-sm">
                <i class="fab fa-telegram" style="margin-right: 8px;"></i>
                Hướng Dẫn Telegram Bot
            </h3>
            <p style="margin: 0; opacity: 0.9; line-height: 1.5;">
                Bot Telegram Giúp Bạn Theo Dõi Bài Học Mới, Tải Tài Liệu Và Nhận Hỗ Trợ Nhanh Chóng.
            </p>
        </div>
        
        <div style="padding: 24px;" class="mobile-p-sm">
            <div style="display: grid; gap: 24px;">
                <!-- Step 1 -->
                <div style="display: flex; gap: 16px;" class="mobile-stack mobile-gap-sm">
                    <div style="width: 40px; height: 40px; background-color: #0088cc; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 700; flex-shrink: 0;">
                        1
                    </div>
                    <div style="flex: 1;">
                        <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                            Tham Gia Nhóm Telegram
                        </h4>
                        <p style="color: #6b7280; margin: 0 0 12px 0; line-height: 1.5;">
                            Click Vào Link "Tham Gia Nhóm" Trong Trang Bot Để Vào Nhóm Telegram Của Môn Học.
                        </p>
                        <div style="background-color: #f0f9ff; padding: 12px; border-radius: 8px; border-left: 4px solid #0088cc;">
                            <p style="margin: 0; color: #0369a1; font-size: 0.875rem;">
                                <strong>Lưu Ý:</strong> Bạn Cần Có Tài Khoản Telegram Để Tham Gia Nhóm.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div style="display: flex; gap: 16px;" class="mobile-stack mobile-gap-sm">
                    <div style="width: 40px; height: 40px; background-color: #0088cc; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 700; flex-shrink: 0;">
                        2
                    </div>
                    <div style="flex: 1;">
                        <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                            Sử Dụng Các Lệnh Bot
                        </h4>
                        <p style="color: #6b7280; margin: 0 0 12px 0; line-height: 1.5;">
                            Gõ Các Lệnh Sau Trong Nhóm Để Tương Tác Với Bot:
                        </p>
                        <div style="display: grid; gap: 12px;">
                            <div style="background-color: #f8fafc; padding: 12px; border-radius: 8px; border: 1px solid #e5e7eb;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <code style="background-color: #0088cc; color: #ffffff; padding: 4px 8px; border-radius: 4px; font-weight: 600;">/bai</code>
                                    <span style="color: #059669; font-size: 0.75rem; font-weight: 600;">Phổ Biến</span>
                                </div>
                                <p style="margin: 0; color: #374151; font-size: 0.875rem;">
                                    Xem Danh Sách 5 Bài Học Mới Nhất
                                </p>
                            </div>
                            <div style="background-color: #f8fafc; padding: 12px; border-radius: 8px; border: 1px solid #e5e7eb;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <code style="background-color: #0088cc; color: #ffffff; padding: 4px 8px; border-radius: 4px; font-weight: 600;">/tai [ID]</code>
                                    <span style="color: #d97706; font-size: 0.75rem; font-weight: 600;">Hữu Ích</span>
                                </div>
                                <p style="margin: 0; color: #374151; font-size: 0.875rem;">
                                    Tải Tài Liệu Bài Học Theo ID (Ví Dụ: /tai 123)
                                </p>
                            </div>
                            <div style="background-color: #f8fafc; padding: 12px; border-radius: 8px; border: 1px solid #e5e7eb;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <code style="background-color: #0088cc; color: #ffffff; padding: 4px 8px; border-radius: 4px; font-weight: 600;">/ho_tro</code>
                                    <span style="color: #dc2626; font-size: 0.75rem; font-weight: 600;">Khẩn Cấp</span>
                                </div>
                                <p style="margin: 0; color: #374151; font-size: 0.875rem;">
                                    Liên Hệ Hỗ Trợ Khi Gặp Vấn Đề
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div style="display: flex; gap: 16px;" class="mobile-stack mobile-gap-sm">
                    <div style="width: 40px; height: 40px; background-color: #0088cc; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 700; flex-shrink: 0;">
                        3
                    </div>
                    <div style="flex: 1;">
                        <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                            Nhận Thông Báo Tự Động
                        </h4>
                        <p style="color: #6b7280; margin: 0 0 12px 0; line-height: 1.5;">
                            Bot Sẽ Tự Động Gửi Thông Báo Khi Có Bài Học Mới Và Nhắc Nhở Học Tập.
                        </p>
                        <div style="background-color: #dcfce7; padding: 12px; border-radius: 8px; border-left: 4px solid #16a34a;">
                            <p style="margin: 0; color: #15803d; font-size: 0.875rem;">
                                <strong>Mẹo:</strong> Bật Thông Báo Cho Nhóm Để Không Bỏ Lỡ Bài Học Mới.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Discord Guide -->
    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #7289da 0%, #5b6eae 100%); padding: 24px; color: #ffffff;" class="mobile-p-sm">
            <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 12px 0;" class="mobile-text-sm">
                <i class="fab fa-discord" style="margin-right: 8px;"></i>
                Hướng Dẫn Discord Bot
            </h3>
            <p style="margin: 0; opacity: 0.9; line-height: 1.5;">
                Discord Bot Cung Cấp Thông Báo Rich Embed Và Báo Cáo Thống Kê Chi Tiết.
            </p>
        </div>
        
        <div style="padding: 24px;" class="mobile-p-sm">
            <div style="display: grid; gap: 24px;">
                <!-- Discord Features -->
                <div>
                    <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0;">
                        Tính Năng Discord Bot
                    </h4>
                    <div style="display: grid; gap: 16px;">
                        <div style="display: flex; gap: 12px; padding: 16px; background-color: #faf5ff; border-radius: 12px; border: 1px solid #d8b4fe;">
                            <div style="width: 32px; height: 32px; background-color: #7289da; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 700; flex-shrink: 0; font-size: 0.875rem;">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div style="flex: 1;">
                                <h5 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 0.875rem;">
                                    Thông Báo Bài Học Mới
                                </h5>
                                <p style="margin: 0; color: #7c3aed; font-size: 0.75rem; line-height: 1.4;">
                                    Nhận Thông Báo Đẹp Mắt Với Rich Embed Khi Có Bài Học Mới
                                </p>
                            </div>
                        </div>
                        
                        <div style="display: flex; gap: 12px; padding: 16px; background-color: #faf5ff; border-radius: 12px; border: 1px solid #d8b4fe;">
                            <div style="width: 32px; height: 32px; background-color: #7289da; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 700; flex-shrink: 0; font-size: 0.875rem;">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div style="flex: 1;">
                                <h5 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 0.875rem;">
                                    Báo Cáo Thống Kê Tuần
                                </h5>
                                <p style="margin: 0; color: #7c3aed; font-size: 0.75rem; line-height: 1.4;">
                                    Nhận Báo Cáo Chi Tiết Về Tiến Độ Học Tập Hàng Tuần
                                </p>
                            </div>
                        </div>
                        
                        <div style="display: flex; gap: 12px; padding: 16px; background-color: #faf5ff; border-radius: 12px; border: 1px solid #d8b4fe;">
                            <div style="width: 32px; height: 32px; background-color: #7289da; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 700; flex-shrink: 0; font-size: 0.875rem;">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div style="flex: 1;">
                                <h5 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 0.875rem;">
                                    Thông Báo Hệ Thống
                                </h5>
                                <p style="margin: 0; color: #7c3aed; font-size: 0.75rem; line-height: 1.4;">
                                    Cập Nhật Về Bảo Trì, Sự Kiện Và Thông Báo Quan Trọng
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); padding: 24px; color: #ffffff;" class="mobile-p-sm">
            <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 12px 0;" class="mobile-text-sm">
                <i class="fas fa-question-circle" style="margin-right: 8px;"></i>
                Câu Hỏi Thường Gặp
            </h3>
            <p style="margin: 0; opacity: 0.9; line-height: 1.5;">
                Giải Đáp Các Thắc Mắc Phổ Biến Về Bot Telegram Và Discord.
            </p>
        </div>
        
        <div style="padding: 24px;" class="mobile-p-sm">
            <div style="display: grid; gap: 20px;">
                <div style="border-bottom: 1px solid #e5e7eb; padding-bottom: 20px;">
                    <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                        Bot Không Phản Hồi Lệnh?
                    </h4>
                    <p style="color: #6b7280; margin: 0; line-height: 1.5; font-size: 0.875rem;">
                        Kiểm Tra Xem Bạn Đã Gõ Đúng Lệnh Chưa. Lệnh Phải Bắt Đầu Bằng Dấu "/" Và Không Có Khoảng Trắng Thừa.
                    </p>
                </div>
                
                <div style="border-bottom: 1px solid #e5e7eb; padding-bottom: 20px;">
                    <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                        Làm Sao Để Tải Tài Liệu?
                    </h4>
                    <p style="color: #6b7280; margin: 0; line-height: 1.5; font-size: 0.875rem;">
                        Sử Dụng Lệnh "/tai [ID]" Với ID Là Số Hiển Thị Trong Danh Sách Bài Học. Ví Dụ: "/tai 123".
                    </p>
                </div>
                
                <div style="border-bottom: 1px solid #e5e7eb; padding-bottom: 20px;">
                    <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                        Không Nhận Được Thông Báo?
                    </h4>
                    <p style="color: #6b7280; margin: 0; line-height: 1.5; font-size: 0.875rem;">
                        Kiểm Tra Cài Đặt Thông Báo Của Nhóm Telegram/Discord. Đảm Bảo Bạn Đã Bật Thông Báo Cho Nhóm.
                    </p>
                </div>
                
                <div>
                    <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                        Cần Hỗ Trợ Thêm?
                    </h4>
                    <p style="color: #6b7280; margin: 0 0 12px 0; line-height: 1.5; font-size: 0.875rem;">
                        Sử Dụng Lệnh "/ho_tro" Trong Bot Hoặc Liên Hệ Trực Tiếp:
                    </p>
                    <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                        @if(contact_email())
                            <a href="mailto:{{ contact_email() }}" 
                               style="background-color: #2563eb; color: #ffffff; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem;"
                               onmouseover="this.style.backgroundColor='#1d4ed8'"
                               onmouseout="this.style.backgroundColor='#2563eb'">
                                <i class="fas fa-envelope" style="margin-right: 6px;"></i>
                                Email
                            </a>
                        @endif
                        
                        @if(contact_phone())
                            <a href="tel:{{ contact_phone() }}" 
                               style="background-color: #059669; color: #ffffff; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem;"
                               onmouseover="this.style.backgroundColor='#047857'"
                               onmouseout="this.style.backgroundColor='#059669'">
                                <i class="fas fa-phone" style="margin-right: 6px;"></i>
                                Điện Thoại
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
