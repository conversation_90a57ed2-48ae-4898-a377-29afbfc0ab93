<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminOnly
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Kiểm Tra Xem User Đã <PERSON>ng <PERSON>p <PERSON>ư<PERSON>
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Vui <PERSON>ng <PERSON>');
        }

        // Kiểm Tra Xem User Có <PERSON> Ad<PERSON>hông
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Bạn Không Có <PERSON>uyền Truy Cập Trang Này');
        }

        return $next($request);
    }
}
