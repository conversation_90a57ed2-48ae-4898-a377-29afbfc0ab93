<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-plus-circle" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.lessons.index') }}"
               style="background-color: #6b7280; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#4b5563'"
               onmouseout="this.style.backgroundColor='#6b7280'">
                <i class="fas fa-arrow-left" style="margin-right: 8px;"></i>
                Quay Lại
            </a>
        </div>
    </x-slot>

    <!-- Form Card -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        <div style="padding: 32px;">
            <form method="POST" action="{{ route('admin.lessons.store') }}" enctype="multipart/form-data">
                @csrf

                <!-- Chọn Môn Học và Chương -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <!-- Chọn Môn Học -->
                    <div>
                        <label for="subject_id" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                            <i class="fas fa-book" style="margin-right: 8px; color: #2563eb;"></i>
                            Chọn Môn Học *
                        </label>
                        <select id="subject_id"
                                name="subject_id"
                                required
                                onchange="loadChapters(this.value)"
                                style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                            <option value="">-- Chọn Môn Học --</option>
                            @foreach($subjects as $subject)
                                <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                    {{ $subject->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('subject_id')
                            <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <!-- Chọn Chương -->
                    <div>
                        <label for="chapter_id" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                            <i class="fas fa-list-alt" style="margin-right: 8px; color: #2563eb;"></i>
                            Chọn Chương *
                        </label>
                        <select id="chapter_id"
                                name="chapter_id"
                                required
                                style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'">
                            <option value="">-- Chọn Chương --</option>
                        </select>
                        @error('chapter_id')
                            <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>

                <!-- Tên Bài Học -->
                <div style="margin-bottom: 24px;">
                    <label for="title" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-video" style="margin-right: 8px; color: #2563eb;"></i>
                        Tên Bài Học *
                    </label>
                    <input type="text"
                           id="title"
                           name="title"
                           value="{{ old('title') }}"
                           required
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="Nhập Tên Bài Học (VD: Bài 1: Giới Thiệu...)">
                    @error('title')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Link YouTube -->
                <div style="margin-bottom: 24px;">
                    <label for="youtube_link" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fab fa-youtube" style="margin-right: 8px; color: #dc2626;"></i>
                        Link Video YouTube (Tùy Chọn)
                    </label>
                    <input type="url"
                           id="youtube_link"
                           name="youtube_link"
                           value="{{ old('youtube_link') }}"
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="https://www.youtube.com/watch?v=...">
                    <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                        <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                        Dán Link Video YouTube Để Học Viên Có Thể Xem Trực Tiếp
                    </div>
                    @error('youtube_link')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Upload File Tài Liệu -->
                <div style="margin-bottom: 24px;">
                    <label for="file_path" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-file-upload" style="margin-right: 8px; color: #2563eb;"></i>
                        Tài Liệu Đính Kèm (Tùy Chọn)
                    </label>
                    <div style="border: 2px dashed #e5e7eb; border-radius: 8px; padding: 24px; text-align: center; transition: all 0.3s ease;"
                         ondragover="event.preventDefault(); this.style.borderColor='#2563eb'; this.style.backgroundColor='#f8fafc';"
                         ondragleave="this.style.borderColor='#e5e7eb'; this.style.backgroundColor='transparent';"
                         ondrop="event.preventDefault(); this.style.borderColor='#e5e7eb'; this.style.backgroundColor='transparent';">
                        <input type="file"
                               id="file_path"
                               name="file_path"
                               accept=".pdf,.doc,.docx,.ppt,.pptx,.zip,.rar"
                               style="display: none;"
                               onchange="previewFile(this)">
                        <div id="upload-area" onclick="document.getElementById('file_path').click()" style="cursor: pointer;">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #9ca3af; margin-bottom: 16px; display: block;"></i>
                            <p style="color: #6b7280; margin: 0 0 8px 0; font-weight: 500;">
                                Nhấp Để Chọn File Hoặc Kéo Thả Vào Đây
                            </p>
                            <p style="color: #9ca3af; margin: 0; font-size: 0.875rem;">
                                Hỗ Trợ: PDF, DOC, DOCX, PPT, PPTX, ZIP, RAR (Tối Đa 10MB)
                            </p>
                        </div>
                        <div id="file-preview" style="display: none; margin-top: 16px;">
                            <div style="background-color: #f3f4f6; border-radius: 8px; padding: 16px; display: inline-flex; align-items: center; gap: 12px;">
                                <i id="file-icon" class="fas fa-file" style="font-size: 24px; color: #6b7280;"></i>
                                <div>
                                    <p id="file-name" style="margin: 0; font-weight: 600; color: #374151;"></p>
                                    <p id="file-size" style="margin: 0; font-size: 0.875rem; color: #6b7280;"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @error('file_path')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Thứ Tự Bài Học -->
                <div style="margin-bottom: 32px;">
                    <label for="order" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-sort-numeric-up" style="margin-right: 8px; color: #2563eb;"></i>
                        Thứ Tự Bài Học (Tùy Chọn)
                    </label>
                    <input type="number"
                           id="order"
                           name="order"
                           value="{{ old('order') }}"
                           min="1"
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="Nhập Số Thứ Tự (VD: 1, 2, 3...)">
                    <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                        <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                        Để Trống Để Tự Động Sắp Xếp Theo Thứ Tự Tạo
                    </div>
                    @error('order')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 16px; justify-content: flex-end; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                    <a href="{{ route('admin.lessons.index') }}"
                       style="background-color: #f3f4f6; color: #374151; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.backgroundColor='#e5e7eb'"
                       onmouseout="this.style.backgroundColor='#f3f4f6'">
                        <i class="fas fa-times" style="margin-right: 8px;"></i>
                        Hủy Bỏ
                    </a>
                    <button type="submit"
                            style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'"
                            onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                        <i class="fas fa-save" style="margin-right: 8px;"></i>
                        Tạo Bài Học
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Info Cards -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-top: 24px;">
        <!-- Video Tips -->
        <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 12px; padding: 24px;">
            <div style="display: flex; align-items: start; gap: 16px;">
                <div style="width: 48px; height: 48px; background-color: #f59e0b; border-radius: 12px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                    <i class="fab fa-youtube" style="font-size: 20px; color: #ffffff;"></i>
                </div>
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #92400e; margin: 0 0 8px 0;">
                        Mẹo Video YouTube
                    </h3>
                    <ul style="color: #b45309; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li>Sử Dụng Link Chia Sẻ Từ YouTube</li>
                        <li>Đảm Bảo Video Công Khai Hoặc Không Được Liệt Kê</li>
                        <li>Kiểm Tra Video Có Phát Được Trên Thiết Bị Khác</li>
                        <li>Tạo Playlist Để Tổ Chức Video Theo Chương</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- File Tips -->
        <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 12px; padding: 24px;">
            <div style="display: flex; align-items: start; gap: 16px;">
                <div style="width: 48px; height: 48px; background-color: #0ea5e9; border-radius: 12px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                    <i class="fas fa-file-alt" style="font-size: 20px; color: #ffffff;"></i>
                </div>
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #0c4a6e; margin: 0 0 8px 0;">
                        Mẹo Tài Liệu
                    </h3>
                    <ul style="color: #0369a1; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li>PDF Cho Slide Bài Giảng</li>
                        <li>DOC/DOCX Cho Tài Liệu Văn Bản</li>
                        <li>PPT/PPTX Cho Bài Thuyết Trình</li>
                        <li>ZIP/RAR Cho Nhiều File Hoặc Code</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Load chapters based on selected subject
        function loadChapters(subjectId) {
            const chapterSelect = document.getElementById('chapter_id');
            chapterSelect.innerHTML = '<option value="">-- Chọn Chương --</option>';

            if (subjectId) {
                const subjects = @json($subjects);
                const selectedSubject = subjects.find(s => s.id == subjectId);

                if (selectedSubject && selectedSubject.chapters) {
                    selectedSubject.chapters.forEach(chapter => {
                        const option = document.createElement('option');
                        option.value = chapter.id;
                        option.textContent = chapter.title;
                        if ('{{ old("chapter_id") }}' == chapter.id) {
                            option.selected = true;
                        }
                        chapterSelect.appendChild(option);
                    });
                }
            }
        }

        // Preview uploaded file
        function previewFile(input) {
            const file = input.files[0];
            if (file) {
                const uploadArea = document.getElementById('upload-area');
                const filePreview = document.getElementById('file-preview');
                const fileName = document.getElementById('file-name');
                const fileSize = document.getElementById('file-size');
                const fileIcon = document.getElementById('file-icon');

                // Hide upload area and show preview
                uploadArea.style.display = 'none';
                filePreview.style.display = 'block';

                // Set file info
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);

                // Set appropriate icon based on file type
                const extension = file.name.split('.').pop().toLowerCase();
                switch (extension) {
                    case 'pdf':
                        fileIcon.className = 'fas fa-file-pdf';
                        fileIcon.style.color = '#dc2626';
                        break;
                    case 'doc':
                    case 'docx':
                        fileIcon.className = 'fas fa-file-word';
                        fileIcon.style.color = '#2563eb';
                        break;
                    case 'ppt':
                    case 'pptx':
                        fileIcon.className = 'fas fa-file-powerpoint';
                        fileIcon.style.color = '#d97706';
                        break;
                    case 'zip':
                    case 'rar':
                        fileIcon.className = 'fas fa-file-archive';
                        fileIcon.style.color = '#059669';
                        break;
                    default:
                        fileIcon.className = 'fas fa-file';
                        fileIcon.style.color = '#6b7280';
                }
            }
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Load chapters on page load if subject is already selected
        document.addEventListener('DOMContentLoaded', function() {
            const subjectSelect = document.getElementById('subject_id');
            if (subjectSelect.value) {
                loadChapters(subjectSelect.value);
            }
        });
    </script>
</x-admin-layout>