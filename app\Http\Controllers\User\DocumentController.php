<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DocumentController extends Controller
{
    /**
     * Hi<PERSON><PERSON>ị <PERSON>ch Tà<PERSON>
     */
    public function index()
    {
        $lessons = Lesson::with(['chapter.subject'])
            ->whereNotNull('file_path')
            ->orderBy('created_at', 'desc')
            ->get();

        // Group lessons by chapter
        $documentsByChapter = $lessons->groupBy(function($lesson) {
            return $lesson->chapter->id;
        })->map(function($chapterLessons) {
            return [
                'chapter' => $chapterLessons->first()->chapter,
                'lessons' => $chapterLessons
            ];
        });

        return view('user.documents.index', compact('documentsByChapter'));
    }

    /**
     * Tải <PERSON>
     */
    public function download(Lesson $lesson)
    {
        if (!$lesson->file_path || !Storage::disk('public')->exists($lesson->file_path)) {
            abort(404, 'Tài liệu không tồn tại');
        }

        return Storage::disk('public')->download($lesson->file_path, $lesson->getFileName());
    }
}
