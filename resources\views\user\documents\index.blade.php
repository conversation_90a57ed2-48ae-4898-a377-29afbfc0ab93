<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-file-alt" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON>
            </h2>
            <div style="display: flex; gap: 8px; align-items: center;">
                <span style="background-color: #dbeafe; color: #2563eb; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                    <i class="fas fa-download" style="margin-right: 4px;"></i>
                    {{ $documentsByChapter->sum(function($chapter) { return $chapter['lessons']->count(); }) }} T<PERSON><PERSON>
                </span>
            </div>
        </div>
    </x-slot>

    <!-- Mobile Responsive CSS -->
    <style>
        @media (max-width: 768px) {
            .mobile-grid { grid-template-columns: 1fr !important; }
            .mobile-text-sm { font-size: 0.875rem !important; }
            .mobile-p-sm { padding: 16px !important; }
            .mobile-gap-sm { gap: 12px !important; }
            .mobile-stack { flex-direction: column !important; align-items: flex-start !important; }
            .mobile-hidden { display: none !important; }
        }
    </style>

    @if($documentsByChapter->count() > 0)
        <!-- Documents by Chapter -->
        <div style="display: grid; gap: 32px;" class="mobile-gap-sm">
            @foreach($documentsByChapter as $chapterData)
                <!-- Chapter Header -->
                <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
                    <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 20px; color: #ffffff;" class="mobile-p-sm">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                            <div>
                                <h3 style="font-size: 1.25rem; font-weight: 700; margin: 0 0 8px 0;" class="mobile-text-sm">
                                    <i class="fas fa-bookmark" style="margin-right: 8px;"></i>
                                    {{ $chapterData['chapter']->title }}
                                </h3>
                                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">
                                    <i class="fas fa-book" style="margin-right: 6px;"></i>
                                    {{ $chapterData['chapter']->subject->title }}
                                </p>
                            </div>
                            <div style="text-align: right;">
                                <span style="background-color: rgba(255, 255, 255, 0.2); padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    <i class="fas fa-file-pdf" style="margin-right: 6px;"></i>
                                    {{ $chapterData['lessons']->count() }} Tài Liệu
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Documents in Chapter -->
                    <div style="padding: 24px;" class="mobile-p-sm">
                        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;" class="mobile-grid mobile-gap-sm">
                            @foreach($chapterData['lessons'] as $lesson)
                                <div style="background-color: #f8fafc; border-radius: 12px; border: 1px solid #e5e7eb; overflow: hidden; transition: all 0.3s ease;"
                                     onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.1)'"
                                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">

                                    <!-- Document Icon -->
                                    <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); padding: 16px; text-align: center;" class="mobile-p-sm">
                                        <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                            <i class="fas fa-file-pdf" style="font-size: 24px; color: #ffffff;"></i>
                                        </div>
                                    </div>

                                    <!-- Document Content -->
                                    <div style="padding: 16px;" class="mobile-p-sm">
                                        <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 12px 0; line-height: 1.3;" class="mobile-text-sm">
                                            {{ $lesson->title }}
                                        </h4>

                                        <!-- Document Actions -->
                                        <div style="display: flex; gap: 8px; align-items: center;" class="mobile-stack mobile-gap-sm">
                                            <a href="{{ route('user.documents.download', $lesson) }}"
                                               style="flex: 1; background-color: #059669; color: #ffffff; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-weight: 600; text-align: center; font-size: 0.875rem; transition: all 0.3s ease;"
                                               onmouseover="this.style.backgroundColor='#047857'"
                                               onmouseout="this.style.backgroundColor='#059669'">
                                                <i class="fas fa-download" style="margin-right: 6px;"></i>
                                                Tải Xuống
                                            </a>

                                            <a href="{{ route('user.subjects.lesson', [$lesson->chapter->subject, $lesson->chapter, $lesson]) }}"
                                               style="background-color: #e5e7eb; color: #374151; padding: 8px 10px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                               onmouseover="this.style.backgroundColor='#d1d5db'"
                                               onmouseout="this.style.backgroundColor='#e5e7eb'"
                                               title="Xem Bài Học">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>

                                        <!-- Document Meta -->
                                        <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #e5e7eb; font-size: 0.75rem; color: #9ca3af; text-align: center;">
                                            <i class="fas fa-calendar-alt" style="margin-right: 4px;"></i>
                                            {{ $lesson->created_at->format('d/m/Y') }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div style="text-align: center; padding: 80px 20px; background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
                <i class="fas fa-file-pdf" style="font-size: 48px; color: #9ca3af;"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin: 0 0 12px 0;">
                Chưa Có Tài Liệu Nào
            </h3>
            <p style="color: #6b7280; margin: 0 0 32px 0; font-size: 1rem; max-width: 400px; margin-left: auto; margin-right: auto;">
                Hiện tại chưa có tài liệu học tập nào. Hãy kiểm tra lại sau hoặc liên hệ với giảng viên.
            </p>
            <div style="display: flex; gap: 16px; justify-content: center; flex-wrap: wrap;">
                <a href="{{ route('user.subjects.index') }}"
                   style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'"
                   onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-book" style="margin-right: 8px;"></i>
                    Xem Môn Học
                </a>
                <a href="{{ route('user.progress.index') }}"
                   style="background-color: #f3f4f6; color: #374151; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                   onmouseout="this.style.backgroundColor='#f3f4f6'">
                    <i class="fas fa-chart-line" style="margin-right: 8px;"></i>
                    Xem Tiến Độ
                </a>
            </div>
        </div>
    @endif
</x-app-layout>
