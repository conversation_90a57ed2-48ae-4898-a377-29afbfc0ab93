<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Subject;
use App\Models\Chapter;
use App\Models\Lesson;
use App\Models\UserSubject;
use Illuminate\Support\Facades\Hash;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo Admin User
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'balance' => 1000000,
                'domain' => request()->getHost() ?? 'localhost',
            ]
        );

        // Tạo Test User
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => Hash::make('password'),
                'role' => 'user',
                'balance' => 500000, // 500k VND
                'domain' => request()->getHost() ?? 'localhost',
            ]
        );

        // Tạo User <PERSON>h<PERSON>o (Không Đủ Tiền)
        $poorUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Poor User',
                'password' => Hash::make('password'),
                'role' => 'user',
                'balance' => 50000, // 50k VND
                'domain' => request()->getHost() ?? 'localhost',
            ]
        );

        // Tạo Subject Miễn Phí
        $freeSubject = Subject::firstOrCreate(
            ['title' => 'Khóa Học HTML CSS Miễn Phí'],
            [
                'description' => 'Học HTML CSS từ cơ bản đến nâng cao hoàn toàn miễn phí',
                'price' => 0,
                'is_free' => true,
                'instructor_name' => 'Thầy Miễn Phí',
            ]
        );

        // Tạo Subject Có Phí
        $paidSubject = Subject::firstOrCreate(
            ['title' => 'Khóa Học Laravel Pro'],
            [
                'description' => 'Khóa học Laravel từ cơ bản đến chuyên nghiệp với nhiều dự án thực tế',
                'price' => 299000, // 299k VND
                'is_free' => false,
                'instructor_name' => 'Thầy Laravel',
            ]
        );

        // Tạo Subject Đắt
        $expensiveSubject = Subject::firstOrCreate(
            ['title' => 'Khóa Học React Native Master'],
            [
                'description' => 'Khóa học React Native chuyên sâu với nhiều ứng dụng thực tế',
                'price' => 799000, // 799k VND
                'is_free' => false,
                'instructor_name' => 'Thầy React',
            ]
        );

        // Tạo Chapters và Lessons cho Free Subject
        $freeChapter1 = Chapter::firstOrCreate([
            'subject_id' => $freeSubject->id,
            'title' => 'Chương 1: Giới Thiệu HTML',
            'order' => 1,
        ]);

        Lesson::firstOrCreate([
            'chapter_id' => $freeChapter1->id,
            'title' => 'Bài 1: HTML Là Gì?',
            'order' => 1,
        ], [
            'youtube_link' => 'https://www.youtube.com/watch?v=example1',
        ]);

        Lesson::firstOrCreate([
            'chapter_id' => $freeChapter1->id,
            'title' => 'Bài 2: Cấu Trúc HTML',
            'order' => 2,
        ], [
            'youtube_link' => 'https://www.youtube.com/watch?v=example2',
        ]);

        // Tạo Chapters và Lessons cho Paid Subject
        $paidChapter1 = Chapter::firstOrCreate([
            'subject_id' => $paidSubject->id,
            'title' => 'Chương 1: Cài Đặt Laravel',
            'order' => 1,
        ]);

        Lesson::firstOrCreate([
            'chapter_id' => $paidChapter1->id,
            'title' => 'Bài 1: Cài Đặt Composer',
            'order' => 1,
        ], [
            'youtube_link' => 'https://www.youtube.com/watch?v=laravel1',
        ]);

        Lesson::firstOrCreate([
            'chapter_id' => $paidChapter1->id,
            'title' => 'Bài 2: Tạo Project Laravel',
            'order' => 2,
        ], [
            'youtube_link' => 'https://www.youtube.com/watch?v=laravel2',
        ]);

        // User mua Paid Subject
        UserSubject::firstOrCreate([
            'user_id' => $user->id,
            'subject_id' => $paidSubject->id,
        ], [
            'amount_paid' => $paidSubject->price,
            'payment_method' => 'balance',
            'transaction_code' => 'TEST_' . time(),
            'status' => 'completed',
            'purchased_at' => now(),
            'domain' => request()->getHost() ?? 'localhost',
        ]);

        $this->command->info('✅ Test Data Đã Được Tạo Thành Công!');
        $this->command->info('📧 Admin: <EMAIL> / password');
        $this->command->info('📧 User (Có Tiền): <EMAIL> / password');
        $this->command->info('📧 User (Nghèo): <EMAIL> / password');
        $this->command->info('🆓 Subject Miễn Phí: ' . $freeSubject->title);
        $this->command->info('💰 Subject Có Phí: ' . $paidSubject->title . ' (' . $paidSubject->formatted_price . ')');
        $this->command->info('💎 Subject Đắt: ' . $expensiveSubject->title . ' (' . $expensiveSubject->formatted_price . ')');
    }
}
