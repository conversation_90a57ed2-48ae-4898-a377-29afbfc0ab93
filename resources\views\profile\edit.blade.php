<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-user-circle" style="margin-right: 8px; color: #2563eb;"></i>
                Thông Tin Cá Nhân
            </h2>
            <div style="display: flex; gap: 8px; align-items: center;">
                <span style="background-color: #dbeafe; color: #2563eb; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                    <i class="fas fa-shield-alt" style="margin-right: 4px;"></i>
                    {{ auth()->user()->role === 'admin' ? 'Quản Trị Viên' : 'Học Viên' }}
                </span>
            </div>
        </div>
    </x-slot>

    @if(session('status') === 'profile-updated')
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; color: #15803d; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            Thông tin cá nhân đã được cập nhật thành công!
        </div>
    @endif

    @if(session('status') === 'password-updated')
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; color: #15803d; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            Mật khẩu đã được thay đổi thành công!
        </div>
    @endif

    <!-- Profile Overview Card -->
    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 32px; color: #ffffff; position: relative; overflow: hidden;" class="mobile-p-sm">
            <div style="position: absolute; top: -20px; right: -20px; width: 120px; height: 120px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1; display: flex; align-items: center; gap: 24px;" class="mobile-stack mobile-gap-sm">
                <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; font-weight: bold; flex-shrink: 0;">
                    {{ strtoupper(substr(auth()->user()->name, 0, 1)) }}
                </div>
                <div style="flex: 1;">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0 0 8px 0;" class="mobile-text-sm">
                        {{ auth()->user()->name }}
                    </h3>
                    <p style="margin: 0 0 8px 0; opacity: 0.9; font-size: 1rem;">
                        <i class="fas fa-envelope" style="margin-right: 8px;"></i>
                        {{ auth()->user()->email }}
                    </p>
                    <div style="display: flex; gap: 16px; align-items: center; flex-wrap: wrap;" class="mobile-stack mobile-gap-sm">
                        <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                            <i class="fas fa-calendar-alt" style="margin-right: 6px;"></i>
                            Tham gia {{ auth()->user()->created_at->format('d/m/Y') }}
                        </span>
                        <span style="background-color: rgba(255, 255, 255, 0.2); padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                            <i class="fas fa-clock" style="margin-right: 6px;"></i>
                            Hoạt động {{ auth()->user()->updated_at->diffForHumans() }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Settings Tabs -->
    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        <!-- Tab Navigation -->
        <div style="background-color: #f8fafc; border-bottom: 1px solid #e5e7eb; padding: 0;">
            <div style="display: flex; overflow-x: auto;" class="mobile-scroll">
                <button type="button" onclick="showProfileTab('info')" id="tab-info"
                        style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #2563eb; cursor: pointer; border-bottom: 3px solid #2563eb; transition: all 0.3s ease; white-space: nowrap;"
                        class="profile-tab-button active">
                    <i class="fas fa-user" style="margin-right: 8px;"></i>
                    Thông Tin Cá Nhân
                </button>
                <button type="button" onclick="showProfileTab('password')" id="tab-password"
                        style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #6b7280; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap;"
                        class="profile-tab-button">
                    <i class="fas fa-lock" style="margin-right: 8px;"></i>
                    Đổi Mật Khẩu
                </button>
                <button type="button" onclick="showProfileTab('security')" id="tab-security"
                        style="padding: 16px 24px; border: none; background: none; font-weight: 600; color: #6b7280; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap;"
                        class="profile-tab-button">
                    <i class="fas fa-shield-alt" style="margin-right: 8px;"></i>
                    Bảo Mật
                </button>
            </div>
        </div>

        <!-- Tab Content -->
        <div style="padding: 32px;" class="mobile-p-sm">
            <!-- Personal Information Tab -->
            <div id="content-info" class="profile-tab-content">
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                    <i class="fas fa-user" style="margin-right: 8px; color: #2563eb;"></i>
                    Cập Nhật Thông Tin Cá Nhân
                </h3>
                <p style="color: #6b7280; margin: 0 0 32px 0;">
                    Cập nhật thông tin hồ sơ và địa chỉ email của tài khoản.
                </p>

                <form method="POST" action="{{ route('profile.update') }}">
                    @csrf
                    @method('patch')

                    <div style="display: grid; gap: 24px;">
                        <!-- Name Field -->
                        <div>
                            <label for="name" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                <i class="fas fa-user" style="margin-right: 8px; color: #2563eb;"></i>
                                Họ và Tên *
                            </label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   value="{{ old('name', auth()->user()->name) }}"
                                   required
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                                   placeholder="Nhập họ và tên của bạn">
                            @error('name')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label for="email" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                <i class="fas fa-envelope" style="margin-right: 8px; color: #2563eb;"></i>
                                Địa Chỉ Email *
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="{{ old('email', auth()->user()->email) }}"
                                   required
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                                   placeholder="Nhập địa chỉ email của bạn">
                            @error('email')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror

                            @if (auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! auth()->user()->hasVerifiedEmail())
                                <div style="margin-top: 12px; padding: 12px; background-color: #fef3c7; border-radius: 8px; border-left: 4px solid #d97706;">
                                    <p style="margin: 0 0 8px 0; color: #92400e; font-size: 0.875rem; font-weight: 600;">
                                        <i class="fas fa-exclamation-triangle" style="margin-right: 6px;"></i>
                                        Email chưa được xác minh
                                    </p>
                                    <p style="margin: 0 0 12px 0; color: #92400e; font-size: 0.875rem;">
                                        Địa chỉ email của bạn chưa được xác minh. Vui lòng kiểm tra email để xác minh.
                                    </p>
                                    <form method="POST" action="{{ route('verification.send') }}">
                                        @csrf
                                        <button type="submit"
                                                style="background-color: #d97706; color: #ffffff; padding: 8px 16px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                                                onmouseover="this.style.backgroundColor='#b45309'"
                                                onmouseout="this.style.backgroundColor='#d97706'">
                                            <i class="fas fa-paper-plane" style="margin-right: 6px;"></i>
                                            Gửi Lại Email Xác Minh
                                        </button>
                                    </form>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                        <button type="submit"
                                style="background-color: #2563eb; color: #ffffff; padding: 12px 32px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 1rem;"
                                onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'"
                                onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                            <i class="fas fa-save" style="margin-right: 8px;"></i>
                            Lưu Thay Đổi
                        </button>
                    </div>
                </form>
            </div>

            <!-- Change Password Tab -->
            <div id="content-password" class="profile-tab-content" style="display: none;">
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                    <i class="fas fa-lock" style="margin-right: 8px; color: #2563eb;"></i>
                    Đổi Mật Khẩu
                </h3>
                <p style="color: #6b7280; margin: 0 0 32px 0;">
                    Đảm bảo tài khoản của bạn sử dụng mật khẩu dài và ngẫu nhiên để giữ an toàn.
                </p>

                <form method="POST" action="{{ route('password.update') }}">
                    @csrf
                    @method('put')

                    <div style="display: grid; gap: 24px;">
                        <!-- Current Password -->
                        <div>
                            <label for="update_password_current_password" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                <i class="fas fa-key" style="margin-right: 8px; color: #2563eb;"></i>
                                Mật Khẩu Hiện Tại *
                            </label>
                            <input type="password"
                                   id="update_password_current_password"
                                   name="current_password"
                                   required
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                                   placeholder="Nhập mật khẩu hiện tại">
                            @error('current_password', 'updatePassword')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- New Password -->
                        <div>
                            <label for="update_password_password" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                <i class="fas fa-lock" style="margin-right: 8px; color: #2563eb;"></i>
                                Mật Khẩu Mới *
                            </label>
                            <input type="password"
                                   id="update_password_password"
                                   name="password"
                                   required
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                                   placeholder="Nhập mật khẩu mới">
                            @error('password', 'updatePassword')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                            <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                Mật khẩu phải có ít nhất 8 ký tự
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="update_password_password_confirmation" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                <i class="fas fa-lock" style="margin-right: 8px; color: #2563eb;"></i>
                                Xác Nhận Mật Khẩu Mới *
                            </label>
                            <input type="password"
                                   id="update_password_password_confirmation"
                                   name="password_confirmation"
                                   required
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                                   placeholder="Nhập lại mật khẩu mới">
                            @error('password_confirmation', 'updatePassword')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                        <button type="submit"
                                style="background-color: #059669; color: #ffffff; padding: 12px 32px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 1rem;"
                                onmouseover="this.style.backgroundColor='#047857'; this.style.transform='translateY(-1px)'"
                                onmouseout="this.style.backgroundColor='#059669'; this.style.transform='translateY(0)'">
                            <i class="fas fa-shield-alt" style="margin-right: 8px;"></i>
                            Cập Nhật Mật Khẩu
                        </button>
                    </div>
                </form>
            </div>

            <!-- Security Tab -->
            <div id="content-security" class="profile-tab-content" style="display: none;">
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                    <i class="fas fa-shield-alt" style="margin-right: 8px; color: #2563eb;"></i>
                    Cài Đặt Bảo Mật
                </h3>
                <p style="color: #6b7280; margin: 0 0 32px 0;">
                    Quản lý các cài đặt bảo mật và quyền riêng tư của tài khoản.
                </p>

                <!-- Account Information -->
                <div style="background-color: #f8fafc; border-radius: 12px; padding: 24px; margin-bottom: 32px; border: 1px solid #e5e7eb;">
                    <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0;">
                        <i class="fas fa-info-circle" style="margin-right: 8px; color: #2563eb;"></i>
                        Thông Tin Tài Khoản
                    </h4>
                    <div style="display: grid; gap: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #e5e7eb;">
                            <div>
                                <p style="margin: 0; font-weight: 600; color: #000000;">ID Tài Khoản</p>
                                <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">{{ auth()->user()->id }}</p>
                            </div>
                            <span style="background-color: #dbeafe; color: #2563eb; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                #{{ str_pad(auth()->user()->id, 6, '0', STR_PAD_LEFT) }}
                            </span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #e5e7eb;">
                            <div>
                                <p style="margin: 0; font-weight: 600; color: #000000;">Trạng Thái Email</p>
                                <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">
                                    {{ auth()->user()->hasVerifiedEmail() ? 'Đã xác minh' : 'Chưa xác minh' }}
                                </p>
                            </div>
                            <span style="background-color: {{ auth()->user()->hasVerifiedEmail() ? '#dcfce7' : '#fef3c7' }}; color: {{ auth()->user()->hasVerifiedEmail() ? '#16a34a' : '#d97706' }}; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                <i class="fas {{ auth()->user()->hasVerifiedEmail() ? 'fa-check-circle' : 'fa-exclamation-triangle' }}" style="margin-right: 4px;"></i>
                                {{ auth()->user()->hasVerifiedEmail() ? 'Đã Xác Minh' : 'Chưa Xác Minh' }}
                            </span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                            <div>
                                <p style="margin: 0; font-weight: 600; color: #000000;">Quyền Truy Cập</p>
                                <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">{{ auth()->user()->role === 'admin' ? 'Quản trị viên' : 'Học viên' }}</p>
                            </div>
                            <span style="background-color: {{ auth()->user()->role === 'admin' ? '#fef3c7' : '#dbeafe' }}; color: {{ auth()->user()->role === 'admin' ? '#d97706' : '#2563eb' }}; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                <i class="fas {{ auth()->user()->role === 'admin' ? 'fa-crown' : 'fa-user' }}" style="margin-right: 4px;"></i>
                                {{ auth()->user()->role === 'admin' ? 'Admin' : 'User' }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Delete Account Section -->
                <div style="background-color: #fef2f2; border-radius: 12px; padding: 24px; border: 1px solid #fecaca;">
                    <h4 style="font-size: 1.125rem; font-weight: 600; color: #dc2626; margin: 0 0 16px 0;">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        Xóa Tài Khoản
                    </h4>
                    <p style="margin: 0 0 20px 0; color: #7f1d1d; font-size: 0.875rem; line-height: 1.5;">
                        Khi tài khoản của bạn bị xóa, tất cả tài nguyên và dữ liệu sẽ bị xóa vĩnh viễn.
                        Trước khi xóa tài khoản, vui lòng tải xuống bất kỳ dữ liệu hoặc thông tin nào bạn muốn giữ lại.
                    </p>

                    <form method="POST" action="{{ route('profile.destroy') }}" onsubmit="return confirmDelete()">
                        @csrf
                        @method('delete')

                        <div style="margin-bottom: 20px;">
                            <label for="password_delete" style="display: block; margin-bottom: 8px; font-weight: 600; color: #dc2626; font-size: 0.875rem;">
                                <i class="fas fa-key" style="margin-right: 8px;"></i>
                                Nhập Mật Khẩu Để Xác Nhận
                            </label>
                            <input type="password"
                                   id="password_delete"
                                   name="password"
                                   required
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #fecaca; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#dc2626'; this.style.boxShadow='0 0 0 3px rgba(220, 38, 38, 0.1)'"
                                   onblur="this.style.borderColor='#fecaca'; this.style.boxShadow='none'"
                                   placeholder="Nhập mật khẩu để xác nhận xóa tài khoản">
                            @error('password', 'userDeletion')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <button type="submit"
                                style="background-color: #dc2626; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                                onmouseover="this.style.backgroundColor='#b91c1c'"
                                onmouseout="this.style.backgroundColor='#dc2626'">
                            <i class="fas fa-trash-alt" style="margin-right: 8px;"></i>
                            Xóa Tài Khoản Vĩnh Viễn
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Tabs -->
    <script>
        function showProfileTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.profile-tab-content').forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.profile-tab-button').forEach(button => {
                button.style.color = '#6b7280';
                button.style.borderBottomColor = 'transparent';
            });

            // Show selected tab content
            document.getElementById('content-' + tabName).style.display = 'block';

            // Add active class to selected tab button
            const activeButton = document.getElementById('tab-' + tabName);
            activeButton.style.color = '#2563eb';
            activeButton.style.borderBottomColor = '#2563eb';
        }

        function confirmDelete() {
            return confirm('Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác!');
        }

        // Show appropriate tab based on errors
        @if($errors->updatePassword->any())
            document.addEventListener('DOMContentLoaded', function() {
                showProfileTab('password');
            });
        @elseif($errors->userDeletion->any())
            document.addEventListener('DOMContentLoaded', function() {
                showProfileTab('security');
            });
        @endif
    </script>
</x-app-layout>
