<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-user-plus" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.users.index') }}"
               style="background-color: #6b7280; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#4b5563'"
               onmouseout="this.style.backgroundColor='#6b7280'">
                <i class="fas fa-arrow-left" style="margin-right: 8px;"></i>
                Quay Lại
            </a>
        </div>
    </x-slot>

    <!-- Form Card -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        <div style="padding: 32px;">
            <form method="POST" action="{{ route('admin.users.store') }}">
                @csrf

                <!-- Họ và Tên -->
                <div style="margin-bottom: 24px;">
                    <label for="name" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-user" style="margin-right: 8px; color: #2563eb;"></i>
                        Họ và Tên *
                    </label>
                    <input type="text"
                           id="name"
                           name="name"
                           value="{{ old('name') }}"
                           required
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="Nhập Họ và Tên Đầy Đủ">
                    @error('name')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Email -->
                <div style="margin-bottom: 24px;">
                    <label for="email" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-envelope" style="margin-right: 8px; color: #2563eb;"></i>
                        Địa Chỉ Email *
                    </label>
                    <input type="email"
                           id="email"
                           name="email"
                           value="{{ old('email') }}"
                           required
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="<EMAIL>">
                    @error('email')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Mật Khẩu -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div>
                        <label for="password" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                            <i class="fas fa-lock" style="margin-right: 8px; color: #2563eb;"></i>
                            Mật Khẩu *
                        </label>
                        <input type="password"
                               id="password"
                               name="password"
                               required
                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                               onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                               placeholder="Nhập Mật Khẩu (Tối Thiểu 8 Ký Tự)">
                        @error('password')
                            <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div>
                        <label for="password_confirmation" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                            <i class="fas fa-lock" style="margin-right: 8px; color: #2563eb;"></i>
                            Xác Nhận Mật Khẩu *
                        </label>
                        <input type="password"
                               id="password_confirmation"
                               name="password_confirmation"
                               required
                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                               onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                               placeholder="Nhập Lại Mật Khẩu">
                    </div>
                </div>

                <!-- Quyền Admin -->
                <div style="margin-bottom: 32px;">
                    <div style="background-color: #f8fafc; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px;">
                        <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                            <input type="checkbox"
                                   id="is_admin"
                                   name="is_admin"
                                   value="1"
                                   {{ old('is_admin') ? 'checked' : '' }}
                                   style="width: 20px; height: 20px; accent-color: #2563eb;">
                            <div>
                                <span style="font-weight: 600; color: #000000; font-size: 1rem;">
                                    <i class="fas fa-user-shield" style="margin-right: 8px; color: #dc2626;"></i>
                                    Cấp Quyền Quản Trị Viên
                                </span>
                                <p style="color: #6b7280; margin: 4px 0 0 0; font-size: 0.875rem;">
                                    Người dùng sẽ có quyền truy cập vào khu vực admin và quản lý toàn bộ hệ thống
                                </p>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 16px; justify-content: flex-end; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                    <a href="{{ route('admin.users.index') }}"
                       style="background-color: #f3f4f6; color: #374151; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.backgroundColor='#e5e7eb'"
                       onmouseout="this.style.backgroundColor='#f3f4f6'">
                        <i class="fas fa-times" style="margin-right: 8px;"></i>
                        Hủy Bỏ
                    </a>
                    <button type="submit"
                            style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'"
                            onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                        <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                        Tạo Người Dùng
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Info Card -->
    <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 12px; padding: 24px; margin-top: 24px;">
        <div style="display: flex; align-items: start; gap: 16px;">
            <div style="width: 48px; height: 48px; background-color: #0ea5e9; border-radius: 12px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                <i class="fas fa-info-circle" style="font-size: 20px; color: #ffffff;"></i>
            </div>
            <div>
                <h3 style="font-size: 1.125rem; font-weight: 600; color: #0c4a6e; margin: 0 0 8px 0;">
                    Lưu Ý Khi Tạo Người Dùng
                </h3>
                <ul style="color: #0369a1; margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li>Email sẽ được tự động xác thực khi tạo từ admin</li>
                    <li>Mật khẩu phải có ít nhất 8 ký tự</li>
                    <li>Quyền admin cho phép truy cập toàn bộ hệ thống</li>
                    <li>Người dùng có thể thay đổi mật khẩu sau khi đăng nhập</li>
                </ul>
            </div>
        </div>
    </div>
</x-admin-layout>