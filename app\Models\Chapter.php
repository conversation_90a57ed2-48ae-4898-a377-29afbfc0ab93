<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Chapter extends Model
{
    protected $fillable = [
        'subject_id',
        'title',
        'order',
    ];

    /**
     * Relationship: <PERSON><PERSON><PERSON> Về <PERSON>t <PERSON>
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Relationship: <PERSON><PERSON><PERSON>ư<PERSON> Có Nhiều <PERSON>
     */
    public function lessons(): HasMany
    {
        return $this->hasMany(Lesson::class)->orderBy('order');
    }

    /**
     * Lấy Tổng Số <PERSON>à<PERSON> Chương
     */
    public function getTotalLessonsAttribute(): int
    {
        return $this->lessons()->count();
    }
}
