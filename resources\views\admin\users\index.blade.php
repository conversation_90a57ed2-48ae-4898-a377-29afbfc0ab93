<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-users" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.users.create') }}"
               style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#1d4ed8'"
               onmouseout="this.style.backgroundColor='#2563eb'">
                <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </a>
        </div>
    </x-slot>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; border-radius: 8px; padding: 16px; margin-bottom: 24px; color: #15803d;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div style="background-color: #fecaca; border: 1px solid #dc2626; border-radius: 8px; padding: 16px; margin-bottom: 24px; color: #dc2626;">
            <i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>
            {{ session('error') }}
        </div>
    @endif

    <!-- Stats Cards -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 32px;">
        <!-- Total Users -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                        Tổng Người Dùng
                    </h4>
                    <p style="font-size: 1.5rem; font-weight: bold; color: #2563eb; margin: 0;">
                        {{ $users->total() }}
                    </p>
                </div>
                <div style="width: 40px; height: 40px; background-color: #dbeafe; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-users" style="font-size: 18px; color: #2563eb;"></i>
                </div>
            </div>
        </div>

        <!-- Admin Users -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                        Quản Trị Viên
                    </h4>
                    <p style="font-size: 1.5rem; font-weight: bold; color: #dc2626; margin: 0;">
                        {{ $users->where('is_admin', true)->count() }}
                    </p>
                </div>
                <div style="width: 40px; height: 40px; background-color: #fecaca; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user-shield" style="font-size: 18px; color: #dc2626;"></i>
                </div>
            </div>
        </div>

        <!-- Regular Users -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                        Người Dùng Thường
                    </h4>
                    <p style="font-size: 1.5rem; font-weight: bold; color: #059669; margin: 0;">
                        {{ $users->where('is_admin', false)->count() }}
                    </p>
                </div>
                <div style="width: 40px; height: 40px; background-color: #d1fae5; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user" style="font-size: 18px; color: #059669;"></i>
                </div>
            </div>
        </div>

        <!-- Verified Users -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h4 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                        Đã Xác Thực Email
                    </h4>
                    <p style="font-size: 1.5rem; font-weight: bold; color: #d97706; margin: 0;">
                        {{ $users->whereNotNull('email_verified_at')->count() }}
                    </p>
                </div>
                <div style="width: 40px; height: 40px; background-color: #fef3c7; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-check-circle" style="font-size: 18px; color: #d97706;"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Users List -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        @if($users->count() > 0)
            <!-- Table Header -->
            <div style="background-color: #f8fafc; padding: 16px 24px; border-bottom: 1px solid #e5e7eb;">
                <div style="display: grid; grid-template-columns: 60px 1fr 200px 150px 120px 180px; gap: 16px; align-items: center; font-weight: 600; color: #374151; font-size: 0.875rem;">
                    <div>STT</div>
                    <div>Thông Tin Người Dùng</div>
                    <div style="text-align: center;">
                        <i class="fas fa-envelope" style="margin-right: 4px;"></i>
                        Email
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-user-shield" style="margin-right: 4px;"></i>
                        Vai Trò
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-check-circle" style="margin-right: 4px;"></i>
                        Trạng Thái
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-cogs" style="margin-right: 4px;"></i>
                        Thao Tác
                    </div>
                </div>
            </div>

            <!-- Table Body -->
            <div>
                @foreach($users as $index => $user)
                    <div style="padding: 20px 24px; border-bottom: 1px solid #f3f4f6; {{ $loop->last ? 'border-bottom: none;' : '' }}">
                        <div style="display: grid; grid-template-columns: 60px 1fr 200px 150px 120px 180px; gap: 16px; align-items: center;">
                            <!-- STT -->
                            <div style="font-weight: 600; color: #6b7280;">
                                {{ ($users->currentPage() - 1) * $users->perPage() + $index + 1 }}
                            </div>

                            <!-- Thông Tin Người Dùng -->
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="width: 48px; height: 48px; background-color: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 600; font-size: 1.125rem;">
                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                </div>
                                <div>
                                    <h3 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 1rem;">
                                        {{ $user->name }}
                                        @if($user->id === auth()->id())
                                            <span style="background-color: #dbeafe; color: #2563eb; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; margin-left: 8px;">
                                                Bạn
                                            </span>
                                        @endif
                                    </h3>
                                    <p style="color: #9ca3af; margin: 0; font-size: 0.75rem;">
                                        <i class="fas fa-calendar-alt" style="margin-right: 4px;"></i>
                                        Tham gia: {{ $user->created_at->format('d/m/Y') }}
                                    </p>
                                </div>
                            </div>

                            <!-- Email -->
                            <div style="text-align: center;">
                                <div style="background-color: #f3f4f6; color: #374151; padding: 6px 12px; border-radius: 6px; font-size: 0.875rem; font-weight: 500;">
                                    {{ $user->email }}
                                </div>
                            </div>

                            <!-- Vai Trò -->
                            <div style="text-align: center;">
                                @if($user->is_admin)
                                    <span style="background-color: #fecaca; color: #dc2626; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                        <i class="fas fa-user-shield" style="margin-right: 4px;"></i>
                                        Admin
                                    </span>
                                @else
                                    <span style="background-color: #d1fae5; color: #059669; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                        <i class="fas fa-user" style="margin-right: 4px;"></i>
                                        User
                                    </span>
                                @endif
                            </div>

                            <!-- Trạng Thái -->
                            <div style="text-align: center;">
                                @if($user->email_verified_at)
                                    <span style="background-color: #dcfce7; color: #16a34a; padding: 6px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; white-space: nowrap;">
                                        <i class="fas fa-check-circle" style="margin-right: 4px;"></i>
                                        Đã Xác Thực
                                    </span>
                                @else
                                    <span style="background-color: #fef3c7; color: #d97706; padding: 6px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; white-space: nowrap;">
                                        <i class="fas fa-clock" style="margin-right: 4px;"></i>
                                        Chưa Xác Thực
                                    </span>
                                @endif
                            </div>

                            <!-- Thao Tác -->
                            <div style="display: flex; gap: 8px; justify-content: center;">
                                <a href="{{ route('admin.users.show', $user) }}"
                                   style="background-color: #f3f4f6; color: #374151; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                                   onmouseout="this.style.backgroundColor='#f3f4f6'"
                                   title="Xem Chi Tiết">
                                    <i class="fas fa-eye"></i>
                                </a>

                                <a href="{{ route('admin.users.edit', $user) }}"
                                   style="background-color: #fef3c7; color: #d97706; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#fde68a'"
                                   onmouseout="this.style.backgroundColor='#fef3c7'"
                                   title="Chỉnh Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>

                                @if($user->id !== auth()->id())
                                    <form method="POST" action="{{ route('admin.users.toggle-admin', $user) }}" style="display: inline; margin: 0;">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit"
                                                style="background-color: {{ $user->is_admin ? '#d1fae5' : '#fecaca' }}; color: {{ $user->is_admin ? '#059669' : '#dc2626' }}; padding: 8px 12px; border-radius: 6px; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.3s ease;"
                                                onmouseover="this.style.opacity='0.8'"
                                                onmouseout="this.style.opacity='1'"
                                                title="{{ $user->is_admin ? 'Hủy Quyền Admin' : 'Cấp Quyền Admin' }}"
                                                onclick="return confirm('{{ $user->is_admin ? 'Bạn có chắc muốn hủy quyền Admin của ' . $user->name . '?' : 'Bạn có chắc muốn cấp quyền Admin cho ' . $user->name . '?' }}')">
                                            <i class="fas {{ $user->is_admin ? 'fa-user-minus' : 'fa-user-plus' }}"></i>
                                        </button>
                                    </form>

                                    <form method="POST" action="{{ route('admin.users.destroy', $user) }}" style="display: inline; margin: 0;"
                                          onsubmit="return confirm('Bạn Có Chắc Muốn Xóa Người Dùng {{ $user->name }}? Hành Động Này Không Thể Hoàn Tác!')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                style="background-color: #fecaca; color: #dc2626; padding: 8px 12px; border-radius: 6px; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.3s ease;"
                                                onmouseover="this.style.backgroundColor='#fca5a5'"
                                                onmouseout="this.style.backgroundColor='#fecaca'"
                                                title="Xóa Người Dùng">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                @else
                                    <span style="background-color: #f3f4f6; color: #9ca3af; padding: 8px 12px; border-radius: 6px; font-size: 0.875rem;">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div style="padding: 20px 24px; border-top: 1px solid #e5e7eb; background-color: #f8fafc;">
                {{ $users->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div style="text-align: center; padding: 60px 20px;">
                <i class="fas fa-users" style="font-size: 64px; color: #d1d5db; margin-bottom: 16px; display: block;"></i>
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #374151; margin: 0 0 8px 0;">
                    Chưa Có Người Dùng Nào
                </h3>
                <p style="color: #6b7280; margin: 0 0 24px 0;">
                    Hãy Tạo Người Dùng Đầu Tiên Để Bắt Đầu Quản Lý Hệ Thống
                </p>
                <a href="{{ route('admin.users.create') }}"
                   style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'"
                   onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                    Tạo Người Dùng Đầu Tiên
                </a>
            </div>
        @endif
    </div>
</x-admin-layout>