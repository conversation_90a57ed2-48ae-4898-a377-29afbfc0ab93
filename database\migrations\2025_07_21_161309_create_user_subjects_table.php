<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_subjects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('ID Người Dùng');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade')->comment('ID Môn Học');
            $table->decimal('amount_paid', 10, 2)->comment('Số Tiền <PERSON>');
            $table->string('payment_method')->nullable()->comment('Phương Thức <PERSON>h Toán');
            $table->string('transaction_code')->nullable()->comment('Mã Giao Dị<PERSON>');
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('completed')->comment('Trạng Thái <PERSON>h <PERSON>án');
            $table->timestamp('purchased_at')->useCurrent()->comment('Thời Gian Mua');
            $table->timestamp('expires_at')->nullable()->comment('Thời Gian Hết Hạn (Nếu Có)');
            $table->text('notes')->nullable()->comment('Ghi Chú');
            $table->string('domain')->nullable()->comment('Tên Miền');
            $table->timestamps();

            // Unique constraint để đảm bảo user không mua trùng subject
            $table->unique(['user_id', 'subject_id'], 'user_subject_unique');

            // Index để tối ưu query
            $table->index(['user_id', 'status']);
            $table->index(['subject_id', 'status']);
            $table->index('domain');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_subjects');
    }
};
