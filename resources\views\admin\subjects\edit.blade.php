<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-edit" style="margin-right: 8px; color: #2563eb;"></i>
                Chỉnh <PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.subjects.index') }}"
               style="background-color: #6b7280; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#4b5563'"
               onmouseout="this.style.backgroundColor='#6b7280'">
                <i class="fas fa-arrow-left" style="margin-right: 8px;"></i>
                Quay Lại
            </a>
        </div>
    </x-slot>

    <!-- Form Card -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        <div style="padding: 32px;">
            <form method="POST" action="{{ route('admin.subjects.update', $subject) }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <!-- Tên Môn Học -->
                <div style="margin-bottom: 24px;">
                    <label for="title" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-book" style="margin-right: 8px; color: #2563eb;"></i>
                        Tên Môn Học *
                    </label>
                    <input type="text"
                           id="title"
                           name="title"
                           value="{{ old('title', $subject->title) }}"
                           required
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="Nhập Tên Môn Học">
                    @error('title')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Mô Tả -->
                <div style="margin-bottom: 24px;">
                    <label for="description" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-align-left" style="margin-right: 8px; color: #2563eb;"></i>
                        Mô Tả Môn Học
                    </label>
                    <textarea id="description"
                              name="description"
                              rows="4"
                              style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff; resize: vertical;"
                              onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                              onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                              placeholder="Nhập Mô Tả Chi Tiết Về Môn Học...">{{ old('description', $subject->description) }}</textarea>
                    @error('description')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Thumbnail -->
                <div style="margin-bottom: 24px;">
                    <label for="thumbnail" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-image" style="margin-right: 8px; color: #2563eb;"></i>
                        Ảnh Thumbnail
                    </label>
                    <div style="border: 2px dashed #e5e7eb; border-radius: 8px; padding: 24px; text-align: center; transition: all 0.3s ease;"
                         ondragover="event.preventDefault(); this.style.borderColor='#2563eb'; this.style.backgroundColor='#f8fafc';"
                         ondragleave="this.style.borderColor='#e5e7eb'; this.style.backgroundColor='transparent';"
                         ondrop="event.preventDefault(); this.style.borderColor='#e5e7eb'; this.style.backgroundColor='transparent';">
                        <input type="file"
                               id="thumbnail"
                               name="thumbnail"
                               accept="image/*"
                               style="display: none;"
                               onchange="previewImage(this)">
                        <div id="upload-area" onclick="document.getElementById('thumbnail').click()" style="cursor: pointer;">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #9ca3af; margin-bottom: 16px; display: block;"></i>
                            <p style="color: #6b7280; margin: 0 0 8px 0; font-weight: 500;">
                                Nhấp Để Chọn Ảnh Hoặc Kéo Thả Vào Đây
                            </p>
                            <p style="color: #9ca3af; margin: 0; font-size: 0.875rem;">
                                Hỗ Trợ: JPG, PNG, GIF (Tối Đa 2MB)
                            </p>
                        </div>
                        <div id="image-preview" style="display: none; margin-top: 16px;">
                            <img id="preview-img" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 1px solid #e5e7eb;">
                            <p id="file-name" style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;"></p>
                        </div>
                    </div>
                    @error('thumbnail')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Link Nhóm Telegram -->
                <div style="margin-bottom: 32px;">
                    <label for="group_link" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fab fa-telegram" style="margin-right: 8px; color: #2563eb;"></i>
                        Link Nhóm Telegram (Tùy Chọn)
                    </label>
                    <input type="url"
                           id="group_link"
                           name="group_link"
                           value="{{ old('group_link', $subject->group_link) }}"
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="https://t.me/your_group_link">
                    @error('group_link')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Instructor Information -->
                <div style="margin-bottom: 32px;">
                    <label for="instructor_name" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                        <i class="fas fa-chalkboard-teacher" style="margin-right: 8px; color: #2563eb;"></i>
                        Tên Giáo Viên
                    </label>
                    <input type="text"
                           id="instructor_name"
                           name="instructor_name"
                           value="{{ old('instructor_name', $subject->instructor_name) }}"
                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                           onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                           placeholder="Nhập tên giáo viên (VD: Thầy Nguyễn Văn A)">
                    @error('instructor_name')
                        <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                            {{ $message }}
                        </div>
                    @enderror
                    <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                        <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                        Tên giáo viên sẽ hiển thị cho học sinh biết ai là người dạy khóa học này
                    </div>
                </div>

                <!-- Pricing Section -->
                <div style="background-color: #f8fafc; border-radius: 12px; padding: 24px; margin-bottom: 32px; border: 1px solid #e5e7eb;">
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 20px 0;">
                        <i class="fas fa-money-bill-wave" style="margin-right: 8px; color: #2563eb;"></i>
                        Thông Tin Giá
                    </h3>

                    <!-- Free Course Toggle -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                            <input type="checkbox"
                                   id="is_free"
                                   name="is_free"
                                   value="1"
                                   {{ old('is_free', $subject->is_free) ? 'checked' : '' }}
                                   onchange="togglePriceField(this)"
                                   style="width: 20px; height: 20px; accent-color: #2563eb;">
                            <span style="font-weight: 600; color: #000000;">
                                <i class="fas fa-gift" style="margin-right: 8px; color: #059669;"></i>
                                Khóa Học Miễn Phí
                            </span>
                        </label>
                    </div>

                    <!-- Price Field -->
                    <div id="price-field" style="display: {{ old('is_free', $subject->is_free) ? 'none' : 'block' }};">
                        <label for="price" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                            <i class="fas fa-tag" style="margin-right: 8px; color: #2563eb;"></i>
                            Giá Khóa Học (VND)
                        </label>
                        <input type="number"
                               id="price"
                               name="price"
                               value="{{ old('price', $subject->price) }}"
                               min="0"
                               step="1000"
                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                               onfocus="this.style.borderColor='#2563eb'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)'"
                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                               placeholder="Nhập giá khóa học (VD: 500000)">
                        @error('price')
                            <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                {{ $message }}
                            </div>
                        @enderror
                        <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                            <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                            Nhập Giá Bằng Số (VD: 500000 -> 500,000 VND)
                        </div>
                    </div>
                </div>

                <!-- Bot Configuration Section -->
                <div style="background-color: #f8fafc; border-radius: 12px; padding: 24px; margin-bottom: 32px; border: 1px solid #e5e7eb;">
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 20px 0;">
                        <i class="fas fa-robot" style="margin-right: 8px; color: #2563eb;"></i>
                        Cấu Hình Bot Telegram & Discord
                    </h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 20px;" class="mobile-grid mobile-gap-sm">
                        <!-- Telegram Group ID -->
                        <div>
                            <label for="telegram_group_id" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                <i class="fab fa-telegram" style="margin-right: 8px; color: #0088cc;"></i>
                                Telegram Group ID
                            </label>
                            <input type="text"
                                   id="telegram_group_id"
                                   name="telegram_group_id"
                                   value="{{ old('telegram_group_id', $subject->telegram_group_id) }}"
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#0088cc'; this.style.boxShadow='0 0 0 3px rgba(0, 136, 204, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                                   placeholder="-1001234567890">
                            @error('telegram_group_id')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                            <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                ID Nhóm Telegram (Bắt Đầu Bằng -100)
                            </div>
                        </div>

                        <!-- Discord Webhook URL -->
                        <div>
                            <label for="discord_webhook_url" style="display: block; margin-bottom: 8px; font-weight: 600; color: #000000; font-size: 0.875rem;">
                                <i class="fab fa-discord" style="margin-right: 8px; color: #7289da;"></i>
                                Discord Webhook URL
                            </label>
                            <input type="url"
                                   id="discord_webhook_url"
                                   name="discord_webhook_url"
                                   value="{{ old('discord_webhook_url', $subject->discord_webhook_url) }}"
                                   style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; background-color: #ffffff;"
                                   onfocus="this.style.borderColor='#7289da'; this.style.boxShadow='0 0 0 3px rgba(114, 137, 218, 0.1)'"
                                   onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
                                   placeholder="https://discord.com/api/webhooks/...">
                            @error('discord_webhook_url')
                                <div style="margin-top: 8px; color: #dc2626; font-size: 14px;">
                                    <i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                            <div style="margin-top: 8px; color: #6b7280; font-size: 0.875rem;">
                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                URL Webhook Discord Channel
                            </div>
                        </div>
                    </div>

                    <!-- Auto Notification Toggle -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                            <input type="hidden" name="auto_notify" value="0">
                            <input type="checkbox"
                                   id="auto_notify"
                                   name="auto_notify"
                                   value="1"
                                   {{ old('auto_notify', $subject->auto_notify) ? 'checked' : '' }}
                                   style="width: 20px; height: 20px; accent-color: #2563eb;">
                            <span style="font-weight: 600; color: #000000;">
                                <i class="fas fa-bell" style="margin-right: 8px; color: #059669;"></i>
                                Tự Động Thông Báo Bài Học Mới
                            </span>
                        </label>
                        <div style="margin-top: 8px; margin-left: 32px; color: #6b7280; font-size: 0.875rem;">
                            Bot Sẽ Tự Động Gửi Thông Báo Khi Có Bài Học Mới
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 16px; justify-content: flex-end; padding-top: 24px; border-top: 1px solid #e5e7eb;">
                    <a href="{{ route('admin.subjects.index') }}"
                       style="background-color: #f3f4f6; color: #374151; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.backgroundColor='#e5e7eb'"
                       onmouseout="this.style.backgroundColor='#f3f4f6'">
                        <i class="fas fa-times" style="margin-right: 8px;"></i>
                        Hủy Bỏ
                    </a>
                    <button type="submit"
                            style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-1px)'"
                            onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                        <i class="fas fa-save" style="margin-right: 8px;"></i>
                        Cập Nhật Môn Học
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript for Image Preview and Price Toggle -->
    <script>
        function previewImage(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('upload-area').style.display = 'none';
                    document.getElementById('image-preview').style.display = 'block';
                    document.getElementById('preview-img').src = e.target.result;
                    document.getElementById('file-name').textContent = file.name;
                };
                reader.readAsDataURL(file);
            }
        }

        function togglePriceField(checkbox) {
            const priceField = document.getElementById('price-field');
            const priceInput = document.getElementById('price');

            if (checkbox.checked) {
                priceField.style.display = 'none';
                priceInput.value = '0';
            } else {
                priceField.style.display = 'block';
                priceInput.focus();
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const isFreeCheckbox = document.getElementById('is_free');
            togglePriceField(isFreeCheckbox);
        });
    </script>
</x-admin-layout>