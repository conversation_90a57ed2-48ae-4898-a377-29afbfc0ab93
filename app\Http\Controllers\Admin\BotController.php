<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Services\TelegramBotService;
use App\Services\DiscordBotService;
use Illuminate\Http\Request;

class BotController extends Controller
{
    protected $telegramBot;
    protected $discordBot;

    public function __construct(TelegramBotService $telegramBot, DiscordBotService $discordBot)
    {
        $this->telegramBot = $telegramBot;
        $this->discordBot = $discordBot;
    }

    /**
     * Hiển Thị Trang Quản Lý Bot
     */
    public function index()
    {
        $subjects = Subject::with('chapters.lessons')->get();

        return view('admin.bots.index', compact('subjects'));
    }

    /**
     * Cập Nhật Cài Đặt Bot Cho Môn H<PERSON>c
     */
    public function updateSubjectBot(Request $request, Subject $subject)
    {
        $request->validate([
            'telegram_group_id' => 'nullable|string',
            'discord_webhook_url' => 'nullable|url',
            'auto_notify' => 'boolean',
        ]);

        $subject->update([
            'telegram_group_id' => $request->telegram_group_id,
            'discord_webhook_url' => $request->discord_webhook_url,
            'auto_notify' => $request->has('auto_notify'),
        ]);

        return redirect()->route('admin.bots.index')
            ->with('success', 'Cài Đặt Bot Đã Được Cập Nhật Thành Công!');
    }

    /**
     * Test Telegram Bot
     */
    public function testTelegram(Request $request, Subject $subject)
    {
        if (!$subject->telegram_group_id) {
            return back()->with('error', 'Chưa Cấu Hình Telegram Group ID!');
        }

        $message = "<b>TEST TELEGRAM BOT</b>\n\n";
        $message .= "<b>Môn Học:</b> {$subject->title}\n";
        $message .= "<b>Thời Gian:</b> " . now()->format('d/m/Y H:i:s') . "\n";
        $message .= "Bot Đang Hoạt Động Bình Thường!";

        $result = $this->telegramBot->sendMessage($subject->telegram_group_id, $message);

        if ($result) {
            return back()->with('success', 'Test Telegram Bot Thành Công!');
        } else {
            return back()->with('error', 'Test Telegram Bot Thất Bại!');
        }
    }

    /**
     * Test Discord Bot
     */
    public function testDiscord(Request $request, Subject $subject)
    {
        if (!$subject->discord_webhook_url) {
            return back()->with('error', 'Chưa Cấu Hình Discord Webhook URL!');
        }

        $result = $this->discordBot->sendSystemNotification(
            $subject->discord_webhook_url,
            'Test Discord Bot',
            "Bot Đang Hoạt Động Bình Thường!\n\nMôn Học: {$subject->title}\nThời Gian: " . now()->format('d/m/Y H:i:s'),
            'info'
        );

        if ($result) {
            return back()->with('success', 'Test Discord Bot Thành Công!');
        } else {
            return back()->with('error', 'Test Discord Bot Thất Bại!');
        }
    }

    /**
     * Gửi Thông Báo Thủ Công
     */
    public function sendManualNotification(Request $request, Subject $subject)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'platform' => 'required|in:telegram,discord,both',
        ]);

        $success = false;
        $errors = [];

        if ($request->platform === 'telegram' || $request->platform === 'both') {
            if ($subject->telegram_group_id) {
                $result = $this->telegramBot->sendMessage($subject->telegram_group_id, $request->message);
                if ($result) {
                    $success = true;
                } else {
                    $errors[] = 'Telegram';
                }
            } else {
                $errors[] = 'Telegram (Chưa Cấu Hình)';
            }
        }

        if ($request->platform === 'discord' || $request->platform === 'both') {
            if ($subject->discord_webhook_url) {
                $result = $this->discordBot->sendSystemNotification(
                    $subject->discord_webhook_url,
                    'Thông Báo Từ Admin',
                    $request->message,
                    'info'
                );
                if ($result) {
                    $success = true;
                } else {
                    $errors[] = 'Discord';
                }
            } else {
                $errors[] = 'Discord (Chưa Cấu Hình)';
            }
        }

        if ($success && empty($errors)) {
            return back()->with('success', 'Thông Báo Đã Được Gửi Thành Công!');
        } elseif ($success && !empty($errors)) {
            return back()->with('warning', 'Thông Báo Đã Được Gửi Một Phần. Lỗi: ' . implode(', ', $errors));
        } else {
            return back()->with('error', 'Gửi Thông Báo Thất Bại: ' . implode(', ', $errors));
        }
    }

    /**
     * Gửi Thống Kê Tuần
     */
    public function sendWeeklyStats(Request $request, Subject $subject)
    {
        $success = false;
        $errors = [];

        if ($subject->telegram_group_id) {
            $result = $this->telegramBot->sendWeeklyStats($subject->telegram_group_id, $subject);
            if ($result) {
                $success = true;
            } else {
                $errors[] = 'Telegram';
            }
        }

        if ($subject->discord_webhook_url) {
            $result = $this->discordBot->sendWeeklyStats($subject->discord_webhook_url, $subject);
            if ($result) {
                $success = true;
            } else {
                $errors[] = 'Discord';
            }
        }

        if ($success && empty($errors)) {
            return back()->with('success', 'Thống Kê Tuần Đã Được Gửi Thành Công!');
        } elseif ($success && !empty($errors)) {
            return back()->with('warning', 'Thống Kê Đã Được Gửi Một Phần. Lỗi: ' . implode(', ', $errors));
        } else {
            return back()->with('error', 'Gửi Thống Kê Thất Bại: ' . implode(', ', $errors));
        }
    }
}
