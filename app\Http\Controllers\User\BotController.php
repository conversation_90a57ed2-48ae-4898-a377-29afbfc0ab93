<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use Illuminate\Http\Request;

class BotController extends Controller
{
    /**
     * Hi<PERSON>n Thị Trang Bot Telegram & Discord
     */
    public function index()
    {
        $subjects = Subject::whereNotNull('group_link')
            ->orWhereNotNull('telegram_group_id')
            ->orWhereNotNull('discord_webhook_url')
            ->with('chapters.lessons')
            ->get();

        return view('user.bots.index', compact('subjects'));
    }

    /**
     * Hiển Thị <PERSON>ớ<PERSON> Dẫn Sử Dụng Bot
     */
    public function guide()
    {
        return view('user.bots.guide');
    }
}
