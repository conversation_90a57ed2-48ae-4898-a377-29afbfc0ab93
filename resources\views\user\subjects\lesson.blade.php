<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
            <div style="display: flex; align-items: center; gap: 12px;">
                <a href="{{ route('user.subjects.show', $subject) }}"
                   style="background-color: #f3f4f6; color: #374151; padding: 8px 12px; border-radius: 8px; text-decoration: none; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                   onmouseout="this.style.backgroundColor='#f3f4f6'">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                        {{ $lesson->title }}
                    </h2>
                    <p style="color: #6b7280; margin: 4px 0 0 0; font-size: 0.875rem;">
                        <a href="{{ route('user.subjects.show', $subject) }}" style="color: #2563eb; text-decoration: none;">{{ $subject->title }}</a>
                        <span style="margin: 0 8px;">•</span>
                        {{ $chapter->title }}
                    </p>
                </div>
            </div>
            <div style="display: flex; gap: 8px; align-items: center;">
                @if($lesson->hasFile())
                    <a href="{{ route('user.documents.download', $lesson) }}"
                       style="background-color: #059669; color: #ffffff; padding: 8px 16px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.backgroundColor='#047857'"
                       onmouseout="this.style.backgroundColor='#059669'">
                        <i class="fas fa-download" style="margin-right: 6px;"></i>
                        Tải Tài Liệu
                    </a>
                @endif
                @if($subject->group_link)
                    <a href="{{ $subject->group_link }}"
                       target="_blank"
                       style="background-color: #0088cc; color: #ffffff; padding: 8px 16px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.backgroundColor='#006699'"
                       onmouseout="this.style.backgroundColor='#0088cc'">
                        <i class="fab fa-telegram" style="margin-right: 6px;"></i>
                        Nhóm
                    </a>
                @endif
            </div>
        </div>
    </x-slot>

    <!-- Mobile Responsive CSS -->
    <style>
        @media (max-width: 768px) {
            .mobile-grid { grid-template-columns: 1fr !important; }
            .mobile-text-sm { font-size: 0.875rem !important; }
            .mobile-p-sm { padding: 16px !important; }
            .mobile-gap-sm { gap: 12px !important; }
            .mobile-stack { flex-direction: column !important; align-items: flex-start !important; }
            .mobile-hidden { display: none !important; }
            .mobile-full { width: 100% !important; }
        }

    </style>

    <div style="display: grid; grid-template-columns: 1fr 300px; gap: 24px;" class="mobile-grid mobile-gap-sm">
        <!-- Main Content -->
        <div>
            <!-- Video Access -->
            @if($lesson->hasVideo())
                <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px; margin-bottom: 24px;" class="mobile-p-sm">
                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;" class="mobile-stack mobile-gap-sm">
                        <div>
                            <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                                <i class="fab fa-youtube" style="margin-right: 8px; color: #dc2626;"></i>
                                Video Bài Học
                            </h3>
                            <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">
                                <i class="fas fa-lock" style="margin-right: 6px;"></i>
                                Video Riêng Tư - Chỉ Thành Viên Đăng Ký Bằng Gmail Đúng Mới Được Xem Video ( Phát Hiện Nhiều Thiết Bị Chuyển Tới Band Tài Khoản )
                            </p>
                        </div>
                        <div style="display: flex; gap: 12px; align-items: center;" class="mobile-stack mobile-gap-sm">
                            <!-- Watch Video Button -->
                            <a href="{{ $lesson->youtube_link }}"
                               target="_blank"
                               style="background-color: #dc2626; color: #ffffff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; font-size: 0.875rem; display: flex; align-items: center; gap: 8px;"
                               onmouseover="this.style.backgroundColor='#b91c1c'"
                               onmouseout="this.style.backgroundColor='#dc2626'">
                                <i class="fab fa-youtube"></i>
                                Xem Video
                            </a>

                            <!-- Mark Complete Button -->
                            <button id="complete-btn"
                                    onclick="toggleComplete()"
                                    style="background-color: {{ $isCompleted ? '#16a34a' : '#6b7280' }}; color: #ffffff; padding: 12px 20px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem; display: flex; align-items: center; gap: 8px;"
                                    onmouseover="this.style.opacity='0.8'"
                                    onmouseout="this.style.opacity='1'">
                                <i class="fas {{ $isCompleted ? 'fa-check-circle' : 'fa-circle' }}"></i>
                                <span id="complete-text">{{ $isCompleted ? 'Hoàn Thành' : 'Đánh Dấu' }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Lesson Content -->
            @if($lesson->content)
                <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px; margin-bottom: 24px;" class="mobile-p-sm">
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0;">
                        <i class="fas fa-file-text" style="margin-right: 8px; color: #2563eb;"></i>
                        Nội Dung Bài Học
                    </h3>
                    <div style="color: #374151; line-height: 1.7; font-size: 1rem;">
                        {!! nl2br(e($lesson->content)) !!}
                    </div>
                </div>
            @endif

            <!-- File Download -->
            @if($lesson->hasFile())
                <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px; margin-bottom: 24px;" class="mobile-p-sm">
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 16px 0;">
                        <i class="fas fa-file-download" style="margin-right: 8px; color: #059669;"></i>
                        Tài Liệu Đính Kèm
                    </h3>
                    <div style="background-color: #f8fafc; border: 2px dashed #e5e7eb; border-radius: 8px; padding: 20px; text-align: center;">
                        <div style="margin-bottom: 12px;">
                            <i class="fas fa-file-pdf" style="font-size: 48px; color: #dc2626;"></i>
                        </div>
                        <h4 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 8px 0;">
                            {{ $lesson->getFileName() }}
                        </h4>
                        <p style="color: #6b7280; margin: 0 0 16px 0; font-size: 0.875rem;">
                            Tài liệu học tập cho bài học này
                        </p>
                        <a href="{{ route('user.documents.download', $lesson) }}"
                           style="background-color: #059669; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; display: inline-block;"
                           onmouseover="this.style.backgroundColor='#047857'"
                           onmouseout="this.style.backgroundColor='#059669'">
                            <i class="fas fa-download" style="margin-right: 8px;"></i>
                            Tải Xuống
                        </a>
                    </div>
                </div>
            @endif



            <!-- Comments Section -->
            <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px; margin-top: 24px;" class="mobile-p-sm">
                <h3 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 20px 0;">
                    <i class="fas fa-comments" style="margin-right: 8px; color: #2563eb;"></i>
                    Bình Luận ({{ $lesson->comments->count() }})
                </h3>

                <!-- Comment Form -->
                <form id="comment-form" style="margin-bottom: 24px;">
                    @csrf
                    <div style="display: flex; gap: 12px; align-items: flex-start;">
                        <div style="width: 40px; height: 40px; background-color: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 600; flex-shrink: 0;">
                            {{ strtoupper(substr(auth()->user()->name, 0, 1)) }}
                        </div>
                        <div style="flex: 1;">
                            <textarea id="comment-content"
                                      name="content"
                                      placeholder="Viết bình luận của bạn..."
                                      style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; resize: vertical; min-height: 80px; transition: all 0.3s ease;"
                                      onfocus="this.style.borderColor='#2563eb'"
                                      onblur="this.style.borderColor='#e5e7eb'"></textarea>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 8px;">
                                <span style="color: #6b7280; font-size: 0.75rem;">
                                    <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                    Tối đa 1000 ký tự
                                </span>
                                <button type="submit"
                                        style="background-color: #2563eb; color: #ffffff; padding: 8px 16px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;"
                                        onmouseover="this.style.backgroundColor='#1d4ed8'"
                                        onmouseout="this.style.backgroundColor='#2563eb'">
                                    <i class="fas fa-paper-plane" style="margin-right: 6px;"></i>
                                    Gửi Bình Luận
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Comments List -->
                <div id="comments-list">
                    @forelse($lesson->comments as $comment)
                        <div class="comment-item" data-comment-id="{{ $comment->id }}" style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #f3f4f6;">
                            <div style="display: flex; gap: 12px;">
                                <div style="width: 40px; height: 40px; background-color: #f3f4f6; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #374151; font-weight: 600; flex-shrink: 0;">
                                    {{ strtoupper(substr($comment->user->name, 0, 1)) }}
                                </div>
                                <div style="flex: 1;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <div>
                                            <h4 style="font-weight: 600; color: #000000; margin: 0; font-size: 0.875rem;">
                                                {{ $comment->user->name }}
                                            </h4>
                                            <p style="color: #6b7280; margin: 0; font-size: 0.75rem;">
                                                {{ $comment->created_at->diffForHumans() }}
                                            </p>
                                        </div>
                                        @if($comment->user_id === auth()->id())
                                            <button onclick="deleteComment({{ $comment->id }})"
                                                    style="background: none; border: none; color: #dc2626; cursor: pointer; padding: 4px; border-radius: 4px; transition: all 0.3s ease;"
                                                    onmouseover="this.style.backgroundColor='#fecaca'"
                                                    onmouseout="this.style.backgroundColor='transparent'"
                                                    title="Xóa bình luận">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        @endif
                                    </div>
                                    <p style="color: #374151; margin: 0 0 8px 0; line-height: 1.5;">
                                        {{ $comment->content }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div style="text-align: center; padding: 40px 20px; color: #6b7280;">
                            <i class="fas fa-comments" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <p style="margin: 0; font-size: 1rem;">Chưa có bình luận nào. Hãy là người đầu tiên bình luận!</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="mobile-hidden">
            <!-- Chapter Info -->
            <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 20px; margin-bottom: 20px;">
                <h3 style="font-size: 1rem; font-weight: 600; color: #000000; margin: 0 0 12px 0;">
                    <i class="fas fa-bookmark" style="margin-right: 8px; color: #2563eb;"></i>
                    {{ $chapter->title }}
                </h3>
                <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">
                    {{ $chapter->lessons->count() }} bài học trong chương này
                </p>
            </div>

            <!-- Lessons List -->
            <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
                <div style="background-color: #f8fafc; padding: 16px; border-bottom: 1px solid #e5e7eb;">
                    <h3 style="font-size: 0.875rem; font-weight: 600; color: #374151; margin: 0; text-transform: uppercase; letter-spacing: 0.05em;">
                        Danh Sách Bài Học
                    </h3>
                </div>
                <div style="max-height: 400px; overflow-y: auto;">
                    @foreach($chapter->lessons as $index => $chapterLesson)
                        <a href="{{ route('user.subjects.lesson', [$subject, $chapter, $chapterLesson]) }}"
                           style="display: block; padding: 12px 16px; text-decoration: none; border-bottom: 1px solid #f3f4f6; transition: all 0.3s ease; {{ $chapterLesson->id === $lesson->id ? 'background-color: #dbeafe; border-left: 3px solid #2563eb;' : '' }}"
                           onmouseover="if (this.style.backgroundColor !== 'rgb(219, 234, 254)') { this.style.backgroundColor='#f8fafc'; }"
                           onmouseout="if (this.style.backgroundColor !== 'rgb(219, 234, 254)') { this.style.backgroundColor='transparent'; }">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="width: 24px; height: 24px; background-color: {{ $chapterLesson->id === $lesson->id ? '#2563eb' : '#f3f4f6' }}; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 0.75rem; font-weight: 600; color: {{ $chapterLesson->id === $lesson->id ? '#ffffff' : '#374151' }}; flex-shrink: 0;">
                                    {{ $index + 1 }}
                                </div>
                                <div style="flex: 1; min-width: 0;">
                                    <h4 style="font-size: 0.875rem; font-weight: 600; color: {{ $chapterLesson->id === $lesson->id ? '#2563eb' : '#000000' }}; margin: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                        {{ $chapterLesson->title }}
                                    </h4>
                                </div>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Interactive Features -->
    <script>
        // CSRF Token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Toggle Complete Status
        let isCompleted = {{ $isCompleted ? 'true' : 'false' }};

        function toggleComplete() {
            const btn = document.getElementById('complete-btn');
            const text = document.getElementById('complete-text');
            const icon = btn.querySelector('i');

            btn.disabled = true;
            btn.style.opacity = '0.6';

            const url = isCompleted
                ? '{{ route("user.lessons.progress.incomplete", $lesson) }}'
                : '{{ route("user.lessons.progress.complete", $lesson) }}';

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isCompleted = !isCompleted;

                    if (isCompleted) {
                        btn.style.backgroundColor = '#16a34a';
                        icon.className = 'fas fa-check-circle';
                        text.textContent = 'Đã Hoàn Thành';
                    } else {
                        btn.style.backgroundColor = '#6b7280';
                        icon.className = 'fas fa-circle';
                        text.textContent = 'Đánh Dấu Hoàn Thành';
                    }

                    // Show success message
                    showMessage(data.message, 'success');
                } else {
                    showMessage('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
            })
            .finally(() => {
                btn.disabled = false;
                btn.style.opacity = '1';
            });
        }

        // Comment Form Submission
        document.getElementById('comment-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const content = document.getElementById('comment-content').value.trim();
            if (!content) {
                showMessage('Vui lòng nhập nội dung bình luận.', 'error');
                return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 6px;"></i>Đang gửi...';

            fetch('{{ route("user.lessons.comments.store", $lesson) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({ content: content })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('comment-content').value = '';
                    showMessage(data.message, 'success');
                    // Reload page to show new comment
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 6px;"></i>Gửi Bình Luận';
            });
        });

        // Delete Comment
        function deleteComment(commentId) {
            if (!confirm('Bạn có chắc chắn muốn xóa bình luận này?')) {
                return;
            }

            fetch(`/user/comments/${commentId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.querySelector(`[data-comment-id="${commentId}"]`).remove();
                    showMessage(data.message, 'success');
                } else {
                    showMessage('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
            });
        }

        // Show Message
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 600;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background-color: ${type === 'success' ? '#16a34a' : '#dc2626'};
            `;
            messageDiv.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}" style="margin-right: 8px;"></i>
                ${message}
            `;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => messageDiv.remove(), 300);
            }, 3000);
        }

        // CSS Animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</x-app-layout>