<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserSubject extends Model
{
    protected $fillable = [
        'user_id',
        'subject_id',
        'amount_paid',
        'payment_method',
        'transaction_code',
        'status',
        'purchased_at',
        'expires_at',
        'notes',
        'domain',
    ];

    protected $casts = [
        'amount_paid' => 'decimal:2',
        'purchased_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Relationship: UserSubject Thuộc Về User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relationship: UserSubject Thuộc Về Subject
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Scope: Lọc Theo Domain
     */
    public function scopeByDomain($query, $domain = null)
    {
        $domain = $domain ?? request()->getHost();
        return $query->where('domain', $domain);
    }

    /**
     * Scope: <PERSON><PERSON><PERSON>
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope: Lọc Theo User
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope: Lọc Theo Subject
     */
    public function scopeBySubject($query, $subjectId)
    {
        return $query->where('subject_id', $subjectId);
    }

    /**
     * Kiểm Tra Xem Có Hết Hạn Không
     */
    public function isExpired(): bool
    {
        if (!$this->expires_at) {
            return false; // Không có thời hạn = không bao giờ hết hạn
        }

        return $this->expires_at->isPast();
    }

    /**
     * Kiểm Tra Xem Có Thể Truy Cập Không
     */
    public function canAccess(): bool
    {
        return $this->status === 'completed' && !$this->isExpired();
    }

    /**
     * Lấy Formatted Amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount_paid, 0, ',', '.') . ' VND';
    }
}
