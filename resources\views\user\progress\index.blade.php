<x-app-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-chart-line" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <div style="display: flex; gap: 8px; align-items: center;">
                <span style="background-color: #dcfce7; color: #16a34a; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                    <i class="fas fa-trophy" style="margin-right: 4px;"></i>
                    {{ $progressPercentage }}% <PERSON><PERSON>n Th<PERSON>
                </span>
            </div>
        </div>
    </x-slot>

    <!-- Mobile Responsive CSS -->
    <style>
        @media (max-width: 768px) {
            .mobile-grid { grid-template-columns: 1fr !important; }
            .mobile-grid-2 { grid-template-columns: repeat(2, 1fr) !important; }
            .mobile-text-sm { font-size: 0.875rem !important; }
            .mobile-p-sm { padding: 16px !important; }
            .mobile-gap-sm { gap: 12px !important; }
            .mobile-stack { flex-direction: column !important; align-items: flex-start !important; }
        }
    </style>

    <!-- Overview Stats -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 20px; margin-bottom: 32px;" class="mobile-grid-2 mobile-gap-sm">
        <!-- Total Progress -->
        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;" class="mobile-p-sm">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-chart-pie" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">Tổng Tiến Độ</span>
                </div>
                <h3 style="font-size: 2.5rem; font-weight: bold; margin: 0 0 8px 0;">{{ $progressPercentage }}%</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">{{ $completedLessons }}/{{ $totalLessons }} Bài Học</p>
            </div>
        </div>

        <!-- Subjects Completed -->
        <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;" class="mobile-p-sm">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-graduation-cap" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">Môn Học</span>
                </div>
                <h3 style="font-size: 2.5rem; font-weight: bold; margin: 0 0 8px 0;">{{ $totalSubjects }}</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Tổng Môn Học</p>
            </div>
        </div>

        <!-- Lessons Completed -->
        <div style="background: linear-gradient(135deg, #d97706 0%, #b45309 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;" class="mobile-p-sm">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-check-circle" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">Hoàn Thành</span>
                </div>
                <h3 style="font-size: 2.5rem; font-weight: bold; margin: 0 0 8px 0;">{{ $completedLessons }}</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Bài Đã Học</p>
            </div>
        </div>

        <!-- Study Time -->
        <div style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); border-radius: 16px; padding: 24px; color: #ffffff; position: relative; overflow: hidden;" class="mobile-p-sm">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 1;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
                    <div style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-clock" style="font-size: 24px;"></i>
                    </div>
                    <span style="font-size: 0.875rem; opacity: 0.8;">Thời Gian</span>
                </div>
                <h3 style="font-size: 2.5rem; font-weight: bold; margin: 0 0 8px 0;">{{ rand(10, 50) }}h</h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Đã Học</p>
            </div>
        </div>
    </div>

    <!-- Progress by Subject -->
    @if($subjects->count() > 0)
        <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; padding: 24px;" class="mobile-p-sm">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: #000000; margin: 0 0 24px 0;">
                <i class="fas fa-book" style="margin-right: 8px; color: #2563eb;"></i>
                Tiến Độ Theo Môn Học
            </h3>
            
            <div style="display: grid; gap: 20px;" class="mobile-gap-sm">
                @foreach($subjects as $subject)
                    <div style="border: 1px solid #f3f4f6; border-radius: 12px; padding: 20px; transition: all 0.3s ease;" class="mobile-p-sm"
                         onmouseover="this.style.borderColor='#e5e7eb'; this.style.backgroundColor='#f8fafc'"
                         onmouseout="this.style.borderColor='#f3f4f6'; this.style.backgroundColor='transparent'">
                        
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;" class="mobile-stack mobile-gap-sm">
                            <div style="flex: 1;">
                                <h4 style="font-size: 1.125rem; font-weight: 600; color: #000000; margin: 0 0 4px 0;" class="mobile-text-sm">
                                    {{ $subject->title }}
                                </h4>
                                <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">
                                    {{ $subject->completed_lessons }}/{{ $subject->lessons_count }} bài học • {{ $subject->chapters_count }} chương
                                </p>
                            </div>
                            <div style="text-align: right;">
                                <span style="background-color: {{ $subject->progress_percentage >= 80 ? '#dcfce7' : ($subject->progress_percentage >= 50 ? '#fef3c7' : '#fecaca') }}; color: {{ $subject->progress_percentage >= 80 ? '#16a34a' : ($subject->progress_percentage >= 50 ? '#d97706' : '#dc2626') }}; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $subject->progress_percentage }}%
                                </span>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div style="margin-bottom: 16px;">
                            <div style="width: 100%; height: 8px; background-color: #f3f4f6; border-radius: 4px; overflow: hidden;">
                                <div style="width: {{ $subject->progress_percentage }}%; height: 100%; background: linear-gradient(90deg, {{ $subject->progress_percentage >= 80 ? '#16a34a' : ($subject->progress_percentage >= 50 ? '#d97706' : '#dc2626') }} 0%, {{ $subject->progress_percentage >= 80 ? '#059669' : ($subject->progress_percentage >= 50 ? '#b45309' : '#b91c1c') }} 100%); border-radius: 4px; transition: width 0.3s ease;"></div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div style="display: flex; gap: 12px; align-items: center;" class="mobile-stack mobile-gap-sm">
                            <a href="{{ route('user.subjects.show', $subject) }}" 
                               style="flex: 1; background-color: #2563eb; color: #ffffff; padding: 10px 16px; border-radius: 8px; text-decoration: none; font-weight: 600; text-align: center; font-size: 0.875rem; transition: all 0.3s ease;"
                               onmouseover="this.style.backgroundColor='#1d4ed8'"
                               onmouseout="this.style.backgroundColor='#2563eb'">
                                <i class="fas fa-play" style="margin-right: 6px;"></i>
                                Tiếp Tục Học
                            </a>
                            
                            @if($subject->progress_percentage >= 100)
                                <span style="background-color: #dcfce7; color: #16a34a; padding: 10px 16px; border-radius: 8px; font-size: 0.875rem; font-weight: 600;">
                                    <i class="fas fa-trophy" style="margin-right: 6px;"></i>
                                    Hoàn Thành
                                </span>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @else
        <!-- Empty State -->
        <div style="text-align: center; padding: 80px 20px; background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
                <i class="fas fa-chart-line" style="font-size: 48px; color: #9ca3af;"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin: 0 0 12px 0;">
                Chưa Có Tiến Độ Nào
            </h3>
            <p style="color: #6b7280; margin: 0 0 32px 0; font-size: 1rem; max-width: 400px; margin-left: auto; margin-right: auto;">
                Bắt đầu học các môn học để theo dõi tiến độ của bạn.
            </p>
            <a href="{{ route('user.subjects.index') }}" 
               style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#1d4ed8'"
               onmouseout="this.style.backgroundColor='#2563eb'">
                <i class="fas fa-book" style="margin-right: 8px;"></i>
                Xem Môn Học
            </a>
        </div>
    @endif
</x-app-layout>
