<?php

namespace App\Http\Controllers\Admin\Bank;

use App\Http\Controllers\Controller;
use App\Models\BankAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BankController extends Controller
{
    public function bank()
    {
        return view('admin.bank.index');
    }

    public function store(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'logo' => 'required',
            'bank_name' => 'required',
            'bank_key' => 'required',
            'account_number' => 'required',
            'account_name' => 'required',
            'branch' => 'nullable',
            'status' => 'required|in:active,inactive',
            'api_key' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $accountBank = BankAccount::where('bank_key', $request->bank_key)->where('domain', request()->getHost())->first();
        if ($accountBank) {
            return redirect()->back()->with('error', 'Tài khoản ngân hàng đã tồn tại trên hệ thống')->withInput();
        }
 
        BankAccount::create([
            'bank_name' => $request->bank_name,
            'bank_key' => $request->bank_key,
            'bank_logo' => $request->logo,
            'account_number' => $request->account_number,
            'account_name' => $request->account_name,
            'branch' => $request->branch,
            'api_key' => $request->api_key,
            'status' => $request->status,
            'domain' => request()->getHost()
        ]);

        return redirect()->back()->with('success', 'Thêm tài khoản ngân hàng thành công');
    }

    public function edit($id)
    {

        $bank = BankAccount::where('id', $id)->where('domain', request()->getHost())->first();

        if (!$bank) {
            return redirect()->back()->with('error', 'Không tìm thấy tài khoản ngân hàng');
        }

        return view('admin.bank.edit', compact('bank'));
    }

    public function update(Request $request, $id)
    {
        $valid = Validator::make($request->all(), [
            'logo' => 'required',
            'bank_name' => 'required',
            'bank_key' => 'required',
            'account_number' => 'required',
            'account_name' => 'required',
            'branch' => 'nullable',
            'status' => 'required|in:active,inactive',
            'api_key' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $bank = BankAccount::where('id', $id)->where('domain', request()->getHost())->first();

        if (!$bank) {
            return redirect()->back()->with('error', 'Không tìm thấy tài khoản ngân hàng');
        }
 
        $bank->update([
            'bank_name' => $request->bank_name,
            'bank_key' => $request->bank_key,
            'bank_logo' => $request->logo ?? $bank->bank_logo,
            'account_number' => $request->account_number,
            'account_name' => $request->account_name,
            'branch' => $request->branch,
            'api_key' => $request->api_key,
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'Cập nhật tài khoản ngân hàng thành công');
    }
}



<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Recharge extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'trans_code',
        'type',
        'method',
        'amount',
        'charge',
        'description',
        'status',
        'is_read',
        'paid_at',
        'expired_at',
        'domain',
    ];

    protected $hidden = [
        'domain',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('trans_code')
            ->orWhere('type', 'like', '%' . $search . '%')
            ->orWhere('method', 'like', '%' . $search . '%')
            ->orWhere('amount', 'like', '%' . $search . '%')
            ->orWhere('charge', 'like', '%' . $search . '%')
            ->orWhere('description', 'like', '%' . $search . '%')
            ->orWhere('status', 'like', '%' . $search . '%')
            ->orWhere('is_read', 'like', '%' . $search . '%')
            ->orWhere('paid_at', 'like', '%' . $search . '%')
            ->orWhere('expired_at', 'like', '%' . $search . '%');
    }



}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RechargeCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'code',
        'request_id',
        'type',
        'provider',
        'amount_card',
        'serial',
        'pin',
        'amount',
        'real_amount',
        'status',
        'message',
        'is_read',
        'domain',
    ];

    protected $hidden = [
        'domain',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('code', 'like', '%' . $search . '%')
            ->orWhere('request_id', 'like', '%' . $search . '%')
            ->orWhere('type', 'like', '%' . $search . '%')
            ->orWhere('provider', 'like', '%' . $search . '%')
            ->orWhere('amount_card', 'like', '%' . $search . '%')
            ->orWhere('serial', 'like', '%' . $search . '%')
            ->orWhere('pin', 'like', '%' . $search . '%')
            ->orWhere('amount', 'like', '%' . $search . '%')
            ->orWhere('real_amount', 'like', '%' . $search . '%')
            ->orWhere('status', 'like', '%' . $search . '%')
            ->orWhere('message', 'like', '%' . $search . '%')
            ->orWhere('is_read', 'like', '%' . $search . '%');
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'transaction_id',
        'transaction_code',
        'type',
        'balance_before',
        'balance_after',
        'amount',
        'ip',
        'data',
        'description',
        'status',
        'domain'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('transaction_id', 'like', '%' . $search . '%')
            ->orWhere('transaction_code', 'like', '%' . $search . '%')
            ->orWhere('type', 'like', '%' . $search . '%')
            ->orWhere('balance_before', 'like', '%' . $search . '%')
            ->orWhere('balance_after', 'like', '%' . $search . '%')
            ->orWhere('amount', 'like', '%' . $search . '%')
            ->orWhere('ip', 'like', '%' . $search . '%')
            ->orWhere('description', 'like', '%' . $search . '%')
            ->orWhere('status', 'like', '%' . $search . '%');
    }

    public function scopeFilter($query, $filter)
    {
       if(isset($filter['type']) && $filter['type'] != '') {
           $query->where('type', $filter['type']);
       }
    }
}
<?php

namespace App\Http\Controllers\Admin\Bank;

use App\Http\Controllers\Controller;
use App\Models\BankAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BankController extends Controller
{
    public function bank()
    {
        return view('admin.bank.index');
    }

    public function store(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'logo' => 'required',
            'bank_name' => 'required',
            'bank_key' => 'required',
            'account_number' => 'required',
            'account_name' => 'required',
            'branch' => 'nullable',
            'status' => 'required|in:active,inactive',
            'api_key' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $accountBank = BankAccount::where('bank_key', $request->bank_key)->where('domain', request()->getHost())->first();
        if ($accountBank) {
            return redirect()->back()->with('error', 'Tài khoản ngân hàng đã tồn tại trên hệ thống')->withInput();
        }
 
        BankAccount::create([
            'bank_name' => $request->bank_name,
            'bank_key' => $request->bank_key,
            'bank_logo' => $request->logo,
            'account_number' => $request->account_number,
            'account_name' => $request->account_name,
            'branch' => $request->branch,
            'api_key' => $request->api_key,
            'status' => $request->status,
            'domain' => request()->getHost()
        ]);

        return redirect()->back()->with('success', 'Thêm tài khoản ngân hàng thành công');
    }

    public function edit($id)
    {

        $bank = BankAccount::where('id', $id)->where('domain', request()->getHost())->first();

        if (!$bank) {
            return redirect()->back()->with('error', 'Không tìm thấy tài khoản ngân hàng');
        }

        return view('admin.bank.edit', compact('bank'));
    }

    public function update(Request $request, $id)
    {
        $valid = Validator::make($request->all(), [
            'logo' => 'required',
            'bank_name' => 'required',
            'bank_key' => 'required',
            'account_number' => 'required',
            'account_name' => 'required',
            'branch' => 'nullable',
            'status' => 'required|in:active,inactive',
            'api_key' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $bank = BankAccount::where('id', $id)->where('domain', request()->getHost())->first();

        if (!$bank) {
            return redirect()->back()->with('error', 'Không tìm thấy tài khoản ngân hàng');
        }
 
        $bank->update([
            'bank_name' => $request->bank_name,
            'bank_key' => $request->bank_key,
            'bank_logo' => $request->logo ?? $bank->bank_logo,
            'account_number' => $request->account_number,
            'account_name' => $request->account_name,
            'branch' => $request->branch,
            'api_key' => $request->api_key,
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'Cập nhật tài khoản ngân hàng thành công');
    }
}
<?php

namespace App\Http\Controllers\Guard;

use App\Http\Controllers\Controller;
use App\Models\BankAccount;
use App\Models\Recharge;
use App\Models\RechargeCard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class RechargeController extends Controller
{
    public function RechargeView()
    {

        $accountBanks = BankAccount::where('domain', request()->getHost())->where('status', 'active')->get();

        return view('guard.recharge.index', compact('accountBanks'));
    }

    public function RechargeCardView()
    {
        return view('guard.recharge.card');
    }

    public function RechargeCard(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'card_type' => 'required|in:VIETTEL,VINAPHONE,MOBIFONE,VIETNAMOBILE',
            'card_value' => 'required|in:10000,20000,30000,50000,100000,200000,300000,500000',
            'card_serial' => 'required',
            'card_code' => 'required',
        ]);

        if ($valid->fails()) {
            return response()->json(['status' => 'error', 'message' => $valid->errors()->first()]);
        }

        $checkCard = RechargeCard::where('serial', $request->card_serial)->where('pin', $request->card_code)->first();

        if ($checkCard) {
            return response()->json(['status' => 'error', 'message' => 'Thẻ đã được sử dụng']);
        }

        $sign = md5(site('partner_key') . $request->card_code . $request->card_serial);
        $request_id = rand(1000, 9999) . time();

        $result = charginCard($request->card_type, $request->card_code, $request->card_serial, $request->card_value, $request_id, site('partner_id'), $sign);

        if (isset($result) && $result['status'] == 99) {
            $request_id_new = $result['request_id'];
            $trans_id = $result['trans_id'];

            RechargeCard::create([
                'user_id' => Auth::id(),
                'code' => $trans_id,
                'request_id' => $request_id_new,
                'type' => 'card',
                'provider' => $request->card_type,
                'amount_card' => $request->card_value,
                'serial' => $request->card_serial,
                'pin' => $request->card_code,
                'status' => 'pending',
                'message' => 'Đang xử lý',
                'domain' => request()->getHost()
            ]);

            return response()->json(['status' => 'success', 'message' => 'Nạp thẻ thành công vui lòng chờ 1-2 phút để kiểm tra']);
        } else {
            if ($result['message'] == 'lang.card_existed') {
                return response()->json(['status' => 'error', 'message' => 'Thẻ đã được sử dụng']);
            }

            return response()->json(['status' => 'error', 'message' => $result['message']]);
        }
    }

    public function CheckRecharge()
    {
        $recharge = Recharge::where('user_id', Auth::id())->where('domain', request()->getHost())->where('is_read', 0)->first();

        if ($recharge) {
            $recharge->is_read = 1;
            $recharge->save();
            return response()->json(['status' => 'success', 'message' => "Bạn đã nạp thành công số tiền: " . number_format($recharge->charge) . "đ"]);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Không có thông báo mới']);
        }
    }
}
<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Controller;
use App\Library\TelegramCustom;
use App\Models\AffiliateCommission;
use App\Models\BankAccount;
use App\Models\Recharge;
use App\Models\RechargeCard;
use App\Models\Site;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request as Psr7Request;
use Illuminate\Http\Request;

class RechargeCronJobController extends Controller
{

    public function rechargeCard(Request $request)
    {
        $site = Site::where('status', 'active')->where('domain', $request->getHost())->first();
        $rechargeCards = RechargeCard::where('status', 'pending')->where('domain', $site->domain)->limit(50)->orderBy('id', 'desc')->get();

        foreach ($rechargeCards as $rechargeCard) {

            $sign = md5($site->partner_key . $rechargeCard->pin . $rechargeCard->serial);

            $result = charginCard($rechargeCard->provider, $rechargeCard->pin, $rechargeCard->serial, $rechargeCard->amount_card, $rechargeCard->request_id, $site->partner_id, $sign, 'check');
            if (isset($result) && $result['status'] == 99) {
                continue;
            }
            if (isset($result) && ($result['status'] == 1 || $result['status'] == 2)) {
                $value = $result['value'] ?? $result['declared_value'];
                $discount = $site->discount_card;
                $fine = 50; 
                $amount = $value - ($value * $discount / 100);
                if ($result['status'] == 2) {
                    $rechargeCard->update([
                        'status' => 'wrong',
                        'message' => $result['message'] . '-' . $result['status'],
                        'amount' => $value,
                        'real_amount' => 0
                    ]);
                    continue;
                } else {
                    $rechargeCard->update([
                        'status' => 'success',
                        'message' => $result['message'] . '-' . $result['status'],
                        'amount' => $value,
                        'real_amount' => $amount
                    ]);
                }

                $user = User::where('domain', $site->domain)->where('id', $rechargeCard->user_id)->first();

                if ($user) {
                    $user->update([
                        'balance' => $user->balance + $amount,
                        'total_deposit' => $user->total_deposit + $amount
                    ]);
                    Transaction::create([
                        'user_id' => $user->id,
                        'transaction_id' => $rechargeCard->request_id,
                        'transaction_code' => bin2hex(random_bytes(5)),
                        'type' => 'recharge-card',
                        'balance_before' => $user->balance,
                        'balance_after' => $user->balance + $amount,
                        'amount' => $amount,
                        'description' => 'Nạp thẻ cào ' . $rechargeCard->provider . ' mệnh giá ' . number_format($value) . ' VNĐ',
                        'ip' => request()->ip(),
                        'data' => json_encode($result),
                        'status' => 'success',
                        'domain' => $site->domain
                    ]);

                    $webhookUrl = site('webhook_discord');
                    $amount_mess = number_format($amount);
                    $money_mess = number_format($user->balance + $amount);
                    $domain_mess = strtoupper($site->domain);
                    $embed = [
                        "title" => "🛒 NẠP THẺ CÀO ".$domain_mess,
                        "color" => hexdec("00cc99"),
                        "fields" => [
                            [
                                "name" => "👤 Tên khách hàng: ".$user->username,
                                "value" => "",
                                "inline" => false
                            ],
                            [
                                "name" => "💰 Số tiền: ".$amount_mess,
                                "value" => "",
                                "inline" => false
                            ],
                            [
                                "name" => "💰 Tổng số dư: ". $money_mess,
                                "value" => "",
                                "inline" => false
                            ],
                            [
                                "name" => "🏦 Nhà mạng: ".$rechargeCard->provider,
                                "value" => "",
                                "inline" => false
                            ],
                            [
                                "name" => "🕒 Thời gian: ".now(),
                                "value" => "",
                                "inline" => false
                            ]
                        ],
                         
                        "timestamp" => now()->toIso8601String(),
                    ];
                    $payload_mess = [
                        "username" => "Đơn Hàng Bot",
                        "avatar_url" => "https://i.ibb.co/wrrywnt4/Blue-White-Futuristic-Simple-Robot-Mascot-Logo.jpg",
                        "embeds" => [$embed]
                    ];
                    $sent = sendMessageDis($webhookUrl, $payload_mess);
                    if ($user->ref_by) {
                        $ref_by = User::where('domain', $site->domain)->where('ref_code', $user->ref_by)->first();
                        if ($ref_by) {
                            $commission = site('commission') ?? 0; // % hoa hồng
                            $amount_commission = $value * $commission / 100;
                            $ref_by->update([
                                'balance_commission' => $ref_by->balance_commission + $amount_commission,
                                'total_commission' => $ref_by->total_commission + $amount_commission
                            ]);

                            Transaction::create([
                                'user_id' => $ref_by->id,
                                'transaction_id' => $rechargeCard->request_id,
                                'transaction_code' => bin2hex(random_bytes(5)),
                                'type' => 'commission',
                                'balance_before' => $ref_by->balance_commission,
                                'balance_after' => $ref_by->balance_commission + $amount_commission,
                                'amount' => $amount_commission,
                                'description' => 'Hoa hồng từ người dùng ' . $user->username . ' nạp thẻ cào ' . $rechargeCard->provider . ' mệnh giá ' . number_format($value) . ' VNĐ',
                                'ip' => request()->ip(),
                                'data' => json_encode($result),
                                'status' => 'success',
                                'domain' => $site->domain
                            ]);

                            AffiliateCommission::create([
                                'user_id' => $user->id,
                                'referrer' => $ref_by->username,
                                'amount' => $value,
                                'commission' => $amount_commission,
                                'status' => 'success',
                                'domain' => $site->domain
                            ]);
                        }
                    }
                }
            } else {
                $rechargeCard->update([
                    'status' => 'error',
                    'message' => $result['message']
                ]);
            }
        }
    }

    public function recharge(Request $request)
    {
        $bank = $request->bank;

        $bank = strtolower($bank);

        if ($bank == 'mbbank') {
            $this->getMbbank();
        }

        if ($bank == 'vietcombank') {
            $this->getVietcombank();
        }

        if ($bank == 'acb') {
            $this->getACB();
        }
    }

    public function getACB()
    {

        $transfer_code = strtolower(site("transfer_code"));
        $min_recharge = site("min_recharge") ?? 0;
        $recharge_promotion = site("recharge_promotion") ?? 0;
        $start_promotion = Carbon::parse(site("start_promotion"));
        $end_promotion = Carbon::parse(site("end_promotion"));

        $acb = BankAccount::where('bank_key', 'acb')->where('domain', request()->getHost())->first();

        if ($acb) {
            $client = new Client();
            $headers = [
                'Cookie' => 'PHPSESSID=r7f3pe7dsv3sjqlknt1jl781cn'
            ];
            $request = new Psr7Request('GET', 'https://api.sieuthicode.net/historyapiacb/' . $acb->api_key, $headers);
            $res = $client->sendAsync($request)->wait();
            $result = json_decode($res->getBody(), true);
            // dd($result);
            if (isset($result['data'])) {
                foreach ($result['data'] as $key => $value) {
                    $tranId = $value['transactionNumber'];
                    $amount = $value['amount'];
                    $description = strtolower($value['description']);

                    if ($amount >= $min_recharge) {

                        if (strpos($description, $transfer_code) !== false) {
                            // $idUser = substr($description, strpos($description, $transfer_code) + strlen($transfer_code), strlen($description));
                            // $idUser = substr($idUser, 0, strpos($idUser, ' '));
                            if (preg_match('/' . $transfer_code . '([\d\s]+)/', $description, $matches)) {
                                $idUser = $matches[1];
                            } else {
                                continue; // Nếu không tìm thấy, bỏ qua
                            }
                            $idUser = str_replace(' ', '', $idUser);
                            $idUser = str_replace('.', '', $idUser);
                            $idUser = str_replace('-', '', $idUser);
                            $idUser = str_replace('-chuyen', '', $idUser);
                            $idUser = str_replace('chuyen', '', $idUser);

                            $user = User::where('domain', request()->getHost())->where('id', $idUser)->first();

                            if (!$user) {
                                continue;
                            };

                            $rechared = Recharge::where('trans_code', $tranId)->where('domain', request()->getHost())->first();

                            if ($rechared) {
                                continue;
                            } else {

                                $amountPromotion = 0;
                                $note = "Nạp thành công tiền vào tài khoản, ngân hàng: " . $acb->bank_name . ", số tiền: " . number_format($amount) . " VNĐ";
                                if ($start_promotion <= Carbon::now() && Carbon::now() <= $end_promotion) {
                                    $amountPromotion = ($amount * $recharge_promotion / 100);
                                    $note = "Nạp thành công tiền vào tài khoản, ngân hàng: " . $acb->bank_name . ", số tiền: " . number_format($amount) . " VNĐ, khuyến mãi: " . number_format($amountPromotion) . " VNĐ";
                                }

                                Recharge::create([
                                    'user_id' => $user->id,
                                    'trans_code' => $tranId,
                                    'type' => 'deposit',
                                    'method' => 'ACB',
                                    'amount' => $amount,
                                    'charge' => $amount + $amountPromotion,
                                    'description' => ucfirst($description),
                                    'status' => 'success',
                                    'domain' => request()->getHost()
                                ]);

                                $user->update([
                                    'balance' => $user->balance + $amount + $amountPromotion,
                                    'total_deposit' => $user->total_deposit + $amount + $amountPromotion
                                ]);

                                Transaction::create([
                                    'user_id' => $user->id,
                                    'transaction_id' => $tranId,
                                    'transaction_code' => bin2hex(random_bytes(5)),
                                    'type' => 'recharge',
                                    'balance_before' => $user->balance - $amount - $amountPromotion,
                                    'balance_after' => $user->balance,
                                    'amount' => $amount + $amountPromotion,
                                    'description' => $note,
                                    'ip' => request()->ip(),
                                    'data' => json_encode($value),
                                    'status' => 'success',
                                    'domain' => request()->getHost()
                                ]);

                                if ($user->ref_by) {
                                    $ref_by = User::where('domain', request()->getHost())->where('ref_code', $user->ref_by)->first();
                                    if ($ref_by) {
                                        $commission = site('commission') ?? 1; // % hoa hồng
                                        $amount_commission = $amount * $commission / 100;
                                        $ref_by->update([
                                            'balance_commission' => $ref_by->balance_commission + $amount_commission,
                                            'total_commission' => $ref_by->total_commission + $amount_commission
                                        ]);

                                        Transaction::create([
                                            'user_id' => $ref_by->id,
                                            'transaction_id' => $tranId,
                                            'transaction_code' => bin2hex(random_bytes(5)),
                                            'type' => 'commission',
                                            'balance_before' => $ref_by->balance_commission,
                                            'balance_after' => $ref_by->balance_commission + $amount_commission,
                                            'amount' => $amount_commission,
                                            'description' => 'Hoa hồng từ người dùng ' . $user->username . ' nạp tiền qua ngân hàng ' . $acb->bank_name . ' số tiền: ' . number_format($amount) . ' VNĐ',
                                            'ip' => request()->ip(),
                                            'data' => json_encode($value),
                                            'status' => 'success',
                                            'domain' => request()->getHost()
                                        ]);

                                        AffiliateCommission::create([
                                            'user_id' => $user->id,
                                            'referrer' => $ref_by->username,
                                            'amount' => $amount,
                                            'commission' => $amount_commission,
                                            'status' => 'success',
                                            'domain' => request()->getHost()
                                        ]);
                                    }
                                }

                                $webhookUrl = site('webhook_discord');
                                $amount_mess = number_format($amount);
                                $money_mess = number_format($user->balance);
                                $domain_mess = strtoupper($site->domain);
                                $km = number_format($amountPromotion);
                                $embed = [
                                    "title" => "🛒 NẠP TIỀN NGÂN HÀNG ".$domain_mess,
                                    "color" => hexdec("00cc99"),
                                    "fields" => [
                                        [
                                            "name" => "👤 Tên khách hàng: ".$user->username,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "💰 Số tiền: ".$amount_mess,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "🎁 Khuyến mãi: ".$km,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "💰 Tổng số dư: ". $money_mess,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "🏦 Ngân hàng: ".$acb->bank_name,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "📝 Ghi chú: ".$note,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "🕒 Thời gian: ".now(),
                                            "value" => "",
                                            "inline" => false
                                        ]
                                    ],
                                    
                                    "timestamp" => now()->toIso8601String(),
                                ];
                                $payload_mess = [
                                    "username" => "Đơn Hàng Bot",
                                    "avatar_url" => "https://i.ibb.co/wrrywnt4/Blue-White-Futuristic-Simple-Robot-Mascot-Logo.jpg",
                                    "embeds" => [$embed]
                                ];
                                $sent = sendMessageDis($webhookUrl, $payload_mess);
                            };
                        }
                    }
                }
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lỗi kết nối với hệ thống ngân hàng'
                ]);
            }
        }
    }

    public function getVietcombank()
    {

        $transfer_code = strtolower(site("transfer_code"));
        $min_recharge = site("min_recharge") ?? 0;
        $recharge_promotion = site("recharge_promotion") ?? 0;
        $start_promotion = Carbon::parse(site("start_promotion"));
        $end_promotion = Carbon::parse(site("end_promotion"));

        $vcb = BankAccount::where('bank_key', 'vietcombank')->where('domain', request()->getHost())->first();

        if ($vcb) {
            $client = new Client();
            $headers = [
                'Cookie' => 'PHPSESSID=r7f3pe7dsv3sjqlknt1jl781cn'
            ];
            $request = new Psr7Request('GET', 'https://api.sieuthicode.net/historyapivcb/' . $vcb->api_key, $headers);
            $res = $client->sendAsync($request)->wait();
            $result = json_decode($res->getBody(), true);
            if (isset($result['transactions'])) {
                foreach ($result['transactions'] as $key => $value) {
                    $tranId = $value['Reference'];
                    $amount = $value['Amount'];
                    $description = strtolower($value['Description']);

                    if ($amount >= $min_recharge) {

                        if (strpos($description, $transfer_code) !== false) {
                            // $idUser = substr($description, strpos($description, $transfer_code) + strlen($transfer_code), strlen($description));
                            // $idUser = substr($idUser, 0, strpos($idUser, ' '));
                            if (preg_match('/' . $transfer_code . '([\d\s]+)/', $description, $matches)) {
                                $idUser = $matches[1];
                            } else {
                                continue; // Nếu không tìm thấy, bỏ qua
                            }

                            $idUser = str_replace(' ', '', $idUser);
                            $idUser = str_replace('.', '', $idUser);
                            $idUser = str_replace('-', '', $idUser);
                            $idUser = str_replace('chuyen', '', $idUser);
                            $idUser = str_replace('-chuyen', '', $idUser);
                            $user = User::where('domain', request()->getHost())->where('id', $idUser)->first();

                            if (!$user) {
                                continue;
                            };

                            $rechared = Recharge::where('trans_code', $tranId)->first();

                            if ($rechared) {
                                continue;
                            };

                            $amountPromotion = 0;
                            $note = "Nạp thành công tiền vào tài khoản, ngân hàng: " . $vcb->bank_name . ", số tiền: " . number_format($amount) . " VNĐ";
                            if ($start_promotion <= Carbon::now() && Carbon::now() <= $end_promotion) {
                                $amountPromotion = ($amount * $recharge_promotion / 100);
                                $note = "Nạp thành công tiền vào tài khoản, ngân hàng: " . $vcb->bank_name . ", số tiền: " . number_format($amount) . " VNĐ, khuyến mãi: " . number_format($amountPromotion) . " VNĐ";
                            }


                            Recharge::create([
                                'user_id' => $user->id,
                                'trans_code' => $tranId,
                                'type' => 'deposit',
                                'method' => 'VIETCOMBANK',
                                'amount' => $amount,
                                'charge' => $amount + $amountPromotion,
                                'description' => ucfirst($description),
                                'status' => 'success',
                                'domain' => request()->getHost()
                            ]);

                            $user->update([
                                'balance' => $user->balance + $amount + $amountPromotion,
                                'total_deposit' => $user->total_deposit + $amount + $amountPromotion
                            ]);
                            $webhookUrl = site('webhook_discord');
                                $amount_mess = number_format($amount);
                                $money_mess = number_format($user->balance);
                                $domain_mess = strtoupper($site->domain);
                                $km = number_format($amountPromotion);
                                $embed = [
                                    "title" => "🛒 NẠP TIỀN NGÂN HÀNG ".$domain_mess,
                                    "color" => hexdec("00cc99"),
                                    "fields" => [
                                        [
                                            "name" => "👤 Tên khách hàng: ".$user->username,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "💰 Số tiền: ".$amount_mess,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "🎁 Khuyến mãi: ".$km,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "💰 Tổng số dư: ". $money_mess,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "🏦 Ngân hàng: ".$vcb->bank_name,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "📝 Ghi chú: ".$note,
                                            "value" => "",
                                            "inline" => false
                                        ],
                                        [
                                            "name" => "🕒 Thời gian: ".now(),
                                            "value" => "",
                                            "inline" => false
                                        ]
                                    ],
                                    
                                    "timestamp" => now()->toIso8601String(),
                                ];
                                $payload_mess = [
                                    "username" => "Đơn Hàng Bot",
                                    "avatar_url" => "https://i.ibb.co/wrrywnt4/Blue-White-Futuristic-Simple-Robot-Mascot-Logo.jpg",
                                    "embeds" => [$embed]
                                ];
                                $sent = sendMessageDis($webhookUrl, $payload_mess);
                            if ($user->ref_by) {
                                $ref_by = User::where('domain', request()->getHost())->where('ref_code', $user->ref_by)->first();
                                if ($ref_by) {
                                    $commission = site('commission') ?? 0; // % hoa hồng
                                    $amount_commission = $amount * $commission / 100;
                                    $ref_by->update([
                                        'balance_commission' => $ref_by->balance_commission + $amount_commission,
                                        'total_commission' => $ref_by->total_commission + $amount_commission
                                    ]);

                                    Transaction::create([
                                        'user_id' => $ref_by->id,
                                        'transaction_id' => $tranId,
                                        'transaction_code' => bin2hex(random_bytes(5)),
                                        'type' => 'commission',
                                        'balance_before' => $ref_by->balance_commission,
                                        'balance_after' => $ref_by->balance_commission + $amount_commission,
                                        'amount' => $amount_commission,
                                        'description' => 'Hoa hồng từ người dùng ' . $user->username . ' nạp tiền qua ngân hàng ' . $vcb->bank_name . ' số tiền: ' . number_format($amount) . ' VNĐ',
                                        'ip' => request()->ip(),
                                        'data' => json_encode($value),
                                        'status' => 'success',
                                        'domain' => request()->getHost()
                                    ]);

                                    AffiliateCommission::create([
                                        'user_id' => $user->id,
                                        'referrer' => $ref_by->username,
                                        'amount' => $amount,
                                        'commission' => $amount_commission,
                                        'status' => 'success',
                                        'domain' => request()->getHost()
                                    ]);
                                }
                            }

                            Transaction::create([
                                'user_id' => $user->id,
                                'transaction_id' => $tranId,
                                'transaction_code' => bin2hex(random_bytes(5)),
                                'type' => 'recharge',
                                'balance_before' => $user->balance - $amount - $amountPromotion,
                                'balance_after' => $user->balance,
                                'amount' => $amount + $amountPromotion,
                                'description' => $note,
                                'ip' => request()->ip(),
                                'data' => json_encode($value),
                                'status' => 'success',
                                'domain' => request()->getHost()
                            ]);

                            
                        }
                    }
                }
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lỗi kết nối với hệ thống ngân hàng'
                ]);
            }
        }
    }

    public function getMbbank()
    {

        $transfer_code = strtolower(site("transfer_code"));
        $min_recharge = site("min_recharge") ?? 0;
        $recharge_promotion = site("recharge_promotion") ?? 0;
        $start_promotion = Carbon::parse(site("start_promotion"));
        $end_promotion = Carbon::parse(site("end_promotion"));

        $mbbank = BankAccount::where('bank_key', 'mbbank')->where('domain', request()->getHost())->first();

        if ($mbbank) {
            $client = new Client();
            $headers = [
                'Cookie' => 'PHPSESSID=r7f3pe7dsv3sjqlknt1jl781cn'
            ];
            $request = new Psr7Request('GET', 'https://api.sieuthicode.net/historyapimbbank/' . $mbbank->api_key, $headers);
            $res = $client->sendAsync($request)->wait();
            $result = json_decode($res->getBody(), true);
            if (isset($result) && $result['status'] == 'success') {
                foreach ($result['TranList'] as $key => $value) {
                    $tranId = $value['tranId'];
                    $amount = $value['creditAmount'];
                    $description = strtolower($value['description']);
                    $description1 = str_replace(" ", "", $description);
                    if ($amount >= $min_recharge) {

                        if (strpos($description1, $transfer_code) !== false) {

                            if (preg_match('/' . $transfer_code . '([\d\s]+)/', $description1, $matches)) {
                                $idUser = $matches[1];
                            } else {
                                continue; // Nếu không tìm thấy, bỏ qua
                            }

                            $idUser = str_replace(' ', '', $idUser);
                            $idUser = str_replace('.', '', $idUser);
                            $idUser = str_replace('-', '', $idUser);
                            $idUser = str_replace('-chuyen', '', $idUser);
                            $idUser = str_replace('chuyen', '', $idUser);
                            $user = User::where('domain', request()->getHost())->where('id', $idUser)->first();

                            if (!$user) {
                                continue;
                            };

                            $rechared = Recharge::where('trans_code', $tranId)->where('domain', request()->getHost())->first();

                            if ($rechared) {
                                continue;
                            };

                            $amountPromotion = 0;
                            $note = "Nạp thành công tiền vào tài khoản, ngân hàng: " . $mbbank->bank_name . ", số tiền: " . number_format($amount) . " VNĐ";
                            if ($start_promotion <= Carbon::now() && Carbon::now() <= $end_promotion) {
                                $amountPromotion = ($amount * $recharge_promotion / 100);
                                $note = "Nạp thành công tiền vào tài khoản, ngân hàng: " . $mbbank->bank_name . ", số tiền: " . number_format($amount) . " VNĐ, khuyến mãi: " . number_format($amountPromotion) . " VNĐ";
                            }

                            Recharge::create([
                                'user_id' => $user->id,
                                'trans_code' => $tranId,
                                'type' => 'deposit',
                                'method' => 'MBBANK',
                                'amount' => $amount,
                                'charge' => $amount + $amountPromotion,
                                'description' => ucfirst($description),
                                'status' => 'success',
                                'domain' => request()->getHost()
                            ]);

                            $user->update([
                                'balance' => $user->balance + $amount + $amountPromotion,
                                'total_deposit' => $user->total_deposit + $amount + $amountPromotion
                            ]);
                             $webhookUrl = site('webhook_discord');
                            $amount_mess = number_format($amount);
                            $money_mess = number_format($user->balance);
                            $domain_mess = strtoupper($site->domain);
                            $km = number_format($amountPromotion);
                            $embed = [
                                "title" => "🛒 NẠP TIỀN NGÂN HÀNG ".$domain_mess,
                                "color" => hexdec("00cc99"),
                                "fields" => [
                                    [
                                        "name" => "👤 Tên khách hàng: ".$user->username,
                                        "value" => "",
                                        "inline" => false
                                    ],
                                    [
                                        "name" => "💰 Số tiền: ".$amount_mess,
                                        "value" => "",
                                        "inline" => false
                                    ],
                                    [
                                        "name" => "🎁 Khuyến mãi: ".$km,
                                        "value" => "",
                                        "inline" => false
                                    ],
                                    [
                                        "name" => "💰 Tổng số dư: ". $money_mess,
                                        "value" => "",
                                        "inline" => false
                                    ],
                                    [
                                        "name" => "🏦 Ngân hàng: ".$acb->bank_name,
                                        "value" => "",
                                        "inline" => false
                                    ],
                                    [
                                        "name" => "📝 Ghi chú: ".$note,
                                        "value" => "",
                                        "inline" => false
                                    ],
                                    [
                                        "name" => "🕒 Thời gian: ".now(),
                                        "value" => "",
                                        "inline" => false
                                    ]
                                ],
                                
                                "timestamp" => now()->toIso8601String(),
                            ];
                            $payload_mess = [
                                "username" => "Đơn Hàng Bot",
                                "avatar_url" => "https://i.ibb.co/wrrywnt4/Blue-White-Futuristic-Simple-Robot-Mascot-Logo.jpg",
                                "embeds" => [$embed]
                            ];
                            $sent = sendMessageDis($webhookUrl, $payload_mess);
                            if ($user->ref_by) {
                                $ref_by = User::where('domain', request()->getHost())->where('ref_code', $user->ref_by)->first();
                                if ($ref_by) {
                                    $commission = site('commission') ?? 0; // % hoa hồng
                                    $amount_commission = $amount * $commission / 100;
                                    $ref_by->update([
                                        'balance_commission' => $ref_by->balance_commission + $amount_commission,
                                        'total_commission' => $ref_by->total_commission + $amount_commission
                                    ]);

                                    Transaction::create([
                                        'user_id' => $ref_by->id,
                                        'transaction_id' => $tranId,
                                        'transaction_code' => bin2hex(random_bytes(5)),
                                        'type' => 'commission',
                                        'balance_before' => $ref_by->balance_commission,
                                        'balance_after' => $ref_by->balance_commission + $amount_commission,
                                        'amount' => $amount_commission,
                                        'description' => 'Hoa hồng từ người dùng ' . $user->username . ' nạp tiền qua ngân hàng ' . $mbbank->bank_name . ' số tiền: ' . number_format($amount) . ' VNĐ',
                                        'ip' => request()->ip(),
                                        'data' => json_encode($value),
                                        'status' => 'success',
                                        'domain' => request()->getHost()
                                    ]);

                                    AffiliateCommission::create([
                                        'user_id' => $user->id,
                                        'referrer' => $ref_by->username,
                                        'amount' => $amount,
                                        'commission' => $amount_commission,
                                        'status' => 'success',
                                        'domain' => request()->getHost()
                                    ]);
                                }
                            }

                            Transaction::create([
                                'user_id' => $user->id,
                                'transaction_id' => $tranId,
                                'transaction_code' => bin2hex(random_bytes(5)),
                                'type' => 'recharge',
                                'balance_before' => $user->balance - $amount - $amountPromotion,
                                'balance_after' => $user->balance,
                                'amount' => $amount + $amountPromotion,
                                'description' => $note,
                                'ip' => request()->ip(),
                                'data' => json_encode($value),
                                'status' => 'success',
                                'domain' => request()->getHost()
                            ]);

                           
                        }
                    }
                }
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lỗi kết nối với hệ thống ngân hàng'
                ]);
            }
        }
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bank_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('bank_name');
            $table->string('bank_key');
            $table->longText('bank_logo')->nullable();
            $table->string('account_number');
            $table->string('account_name');
            $table->string('branch')->nullable();
            $table->longText('api_key')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->string('domain')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_accounts');
    }
};
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recharges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->longText('trans_code')->nullable();
            $table->string('type')->default('deposit'); // deposit, bill
            $table->string('method')->nullable();
            $table->longText('amount')->nullable();
            $table->longText('charge')->nullable(); // là phí
            $table->longText('description')->nullable();
            $table->enum('status', ['pending', 'success', 'failed'])->default('pending');
            $table->boolean('is_read')->default(false);
            $table->timestamp('paid_at')->nullable(); // thời gian thanh toán
            $table->timestamp('expired_at')->nullable(); // thời gian hết hạn
            $table->timestamps();
            $table->string('domain')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recharges');
    }
};
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recharge_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->longText('code')->nullable();
            $table->string('request_id')->nullable();
            $table->string('type')->nullable();
            $table->string('provider')->nullable(); // Viettel, Mobifone, Vinaphone
            $table->string('amount_card')->nullable();
            $table->string('serial')->nullable();
            $table->string('pin')->nullable();
            $table->longText('amount')->nullable();
            $table->longText('real_amount')->nullable();
            $table->string('status')->default('pending');
            $table->string('message')->nullable();
            $table->boolean('is_read')->default(false);
            $table->timestamps();
            $table->string('domain')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recharge_cards');
    }
};
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->longText('transaction_id')->nullable();
            $table->longText('transaction_code')->nullable();
            $table->string('type');
            $table->longText('balance_before');
            $table->longText('balance_after');
            $table->longText('amount'); // amount of money
            $table->string('ip');
            $table->longText('data')->nullable();
            $table->longText('description')->nullable();
            $table->enum('status', ['pending', 'success', 'failed'])->default('pending');
            $table->timestamps();
            $table->string('domain')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
@extends('layouts.admin.admin')
@section('title', 'Chỉnh sửa ngân hàng')
@section('content')
    <div class="card custom-card shadow">
        <div class="card-header bg-primary">
            <h4 class="card-title text-white">Chỉnh sửa ngân hàng</h4>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.bank.update', $bank->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-group mb-3">
                    <label for="logo" class="form-label">Logo Ngân hàng</label>
                    <input type="text" value="{{ $bank->bank_logo }}" class="form-control" id="logo" name="logo">
                  
                </div>
                <div class="form-group mb-3">
                    <label for="bank_name" class="form-label">Ngân hàng</label>
                    <input type="text" class="form-control" id="bank_name" name="bank_name"
                        value="{{ $bank->bank_name }}">
                </div>
                <div class="form-group mb-3">
                    <label for="bank_key" class="form-label">Loại ngân hàng</label>
                    <select class="form-select" name="bank_key" id="bank_key">
                        <option value="mbbank" {{ $bank->bank_key == 'mbbank' ? 'selected' : '' }}>MBBank</option>
                        <option value="vietcombank" {{ $bank->bank_key == 'vietcombank' ? 'selected' : '' }}>
                            Vietcombank</option>
                        <option value="acb" {{ $bank->bank_key == 'acb' ? 'selected' : '' }}>ACB</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="account_name" class="form-label">Chủ tài khoản</label>
                    <input type="text" class="form-control" id="account_name" name="account_name"
                        value="{{ $bank->account_name }}">
                </div>
                <div class="form-group mb-3">
                    <label for="account_number" class="form-label">Số tài khoản</label>
                    <input type="text" class="form-control" id="account_number" name="account_number"
                        value="{{ $bank->account_number }}">
                </div>
                <div class="form-group mb-3">
                    <label for="status" class="form-label">Trạng thái</label>
                    <select class="form-select" name="status" id="status">
                        <option value="active" {{ $bank->status == 'active' ? 'selected' : '' }}>Hoạt động</option>
                        <option value="inactive" {{ $bank->status == 'inactive' ? 'selected' : '' }}>Không hoạt động
                        </option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="api_key" class="form-label">API Key (<a target="_blank" class="text-primary"
                            href="https://api.sieuthicode.net">api.sieuthicode.net</a>)</label>
                    <input type="text" class="form-control" id="api_key" name="api_key"
                        value="{{ $bank->api_key }}">
                </div>
                <button type="submit" class="btn btn-primary">Cập nhật</button>
            </form>
        </div>
    </div>
@endsection
@extends('layouts.admin.admin')
@section('title', 'Danh sách ngân hàng')
@section('content')
    <div class="row">
        <div class="col-md-6">
            <div class="card custom-card shadow">
                <div class="card-header bg-primary">
                    <h4 class="card-title text-white">Cấu hình Nạp tiền</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.config.update') }}" method="POST">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="transfer_code" class="form-label">Mã nạp tiền</label>
                            <input type="text" class="form-control" id="transfer_code" name="transfer_code"
                                value="{{ site('transfer_code') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="min_recharge" class="form-label">Nạp tối thiểu</label>
                            <input type="text" class="form-control" id="min_recharge" name="min_recharge"
                                value="{{ site('min_recharge') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="recharge_promotion" class="form-label">Khuyến mãi nạp tiền %</label>
                            <input type="text" class="form-control" id="recharge_promotion" name="recharge_promotion"
                                value="{{ site('recharge_promotion') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="start_promotion" class="form-label">Thời gian khuyến mãi</label>
                            <input type="date" class="form-control" id="start_promotion" name="start_promotion"
                                value="{{ Carbon\Carbon::parse(site('start_promotion'))->format('Y-m-d') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="end_promotion" class="form-label">Thời gian kết thúc khuyến mãi</label>
                            <input type="date" class="form-control" id="end_promotion" name="end_promotion"
                                value="{{ Carbon\Carbon::parse(site('end_promotion'))->format('Y-m-d') }}">
                        </div>
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card custom-card shadow">
                <div class="card-header bg-primary">
                    <h4 class="card-title text-white">Cấu hình thẻ cào  <a class="text-danger" href="https://thevipre.vn/">THEVIPRE.VN</a></h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.config.update') }}" method="POST">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="partner_id" class="form-label">Partner ID</label>
                            <input type="text" class="form-control" id="partner_id" name="partner_id"
                                value="{{ site('partner_id') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="partner_key" class="form-label">Partner KEY</label>
                            <input type="text" class="form-control" id="partner_key" name="partner_key"
                                value="{{ site('partner_key') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="discount_card" class="form-label">Chiết khấu %</label>
                            <input type="text" class="form-control" id="discount_card" name="discount_card"
                                value="{{ site('discount_card') }}">
                        </div>
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="card custom-card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Danh sách tài khoản</h4>
                    <button type="button" class="btn btn-primary btn-wave" data-bs-toggle="modal"
                        data-bs-target="#exampleModal">
                        Thêm mới
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table w-100" id="table-banks">
                            <thead>
                                <tr>
                                    <th>STT</th>
                                    <th>Thao tác</th>
                                    <th>Ngân hàng</th>
                                    <th>Logo</th>
                                    <th>Chủ tài khoản</th>
                                    <th>Số tài khoản</th>
                                    <th>Trạng thái</th>
                                    <th>Thời gian</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal add bank -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <form action="{{ route('admin.bank.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalLabel">Thêm tài khoản ngân hàng</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group mb-3">
                            <label for="logo" class="form-label">Logo Ngân hàng</label>
                            <input type="text" class="form-control" id="logo" name="logo">
                        </div>
                        <div class="form-group mb-3">
                            <label for="bank_name" class="form-label">Ngân hàng</label>
                            <input type="text" class="form-control" id="bank_name" name="bank_name"
                                value="{{ old('bank_name') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="bank_key" class="form-label">Loại ngân hàng</label>
                            <select class="form-select" name="bank_key" id="bank_key">
                                <option value="mbbank" {{ old('bank_key') == 'mbbank' ? 'selected' : '' }}>MBBank</option>
                                <option value="vietcombank" {{ old('bank_key') == 'vietcombank' ? 'selected' : '' }}>
                                    Vietcombank</option>
                                <option value="acb" {{ old('bank_key') == 'acb' ? 'selected' : '' }}>ACB</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="account_name" class="form-label">Chủ tài khoản</label>
                            <input type="text" class="form-control" id="account_name" name="account_name"
                                value="{{ old('account_name') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="account_number" class="form-label">Số tài khoản</label>
                            <input type="text" class="form-control" id="account_number" name="account_number"
                                value="{{ old('account_number') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="status" class="form-label">Trạng thái</label>
                            <select class="form-select" name="status" id="status">
                                <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Hoạt động
                                </option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Không hoạt
                                    động
                                </option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="api_key" class="form-label">API Key (<a target="_blank" class="text-primary"
                                    href="https://api.sieuthicode.net">api.sieuthicode.net</a>)</label>
                            <input type="text" class="form-control" id="api_key" name="api_key"
                                value="{{ old('api_key') }}">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                        <button type="submit" class="btn btn-primary">Thêm mới</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
@section('script')
    <script>
        $(document).ready(function() {
            loadDatatable('#table-banks', 'banks', [{
                    data: 'id',
                    name: 'id'
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, full, meta) {
                        return `
                            <a href="bank/edit/${full.id}" class="btn btn-primary btn-sm btn-icon">
                                <i class="bi bi-eye"></i>
                            </a>
                            <button class="btn btn-danger btn-sm btn-icon" onclick="deleteData(${full.id}, 'banks')">
                                <i class="bi bi-trash"></i>
                            </button>
                        `;
                    }
                },
                {
                    data: 'bank_name',
                    name: 'bank_name'
                },
                {
                    data: 'bank_logo',
                    name: 'bank_logo',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, full, meta) {
                        return `<img src="${data}" width="50" />`;
                    }
                },
                {
                    data: 'account_name',
                    name: 'account_name'
                },
                {
                    data: 'account_number',
                    name: 'account_number'
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data, type, full, meta) {
                        return data == 'active' ? '<span class="badge bg-success">Hoạt động</span>' :
                            '<span class="badge bg-danger">Không hoạt động</span>';
                    }
                },
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data, type, full, meta) {
                        return moment(data).format('DD/MM/YYYY');
                    }
                }
            ])
        });
    </script>
@endsection
@extends('layouts.guard.guard')
@section('title', 'Nạp thẻ cào')
@section('content')
    <div class="row">
        <div class="col-md-12 mb-3">
            <div class="card">
                <div class="card-body">
                     <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ route('guard.recharge') }}" class="btn btn-outline-primary w-100  rounded-2">
                                <i class="fa-solid fa-credit-card fa-fw"></i>
                                Ngân hàng
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ route('guard.recharge-card') }}"
                                class="btn btn-primary w-100  rounded-2"><i class="fa-solid fa-brands fa-cc-mastercard fa-fw"></i> Thẻ cào</a>
                        </div>
                    </div>
                    <div class="alert alert-light-danger mb-3">
                        <ul>
                            <li class="text-sm fw-semibold">- Nạp đúng seri, mã thẻ, nếu sai thẻ sẽ bị từ chối nạp lại lần
                                2, bạn hãy nạp vào sim sử dụng.</li>
                            <li class="text-sm fw-semibold">- Nạp sai mệnh giá sẽ bị mất thẻ, không hỗ trợ khiếu nại, nên
                                kiểm tra kĩ trước khi nạp.</li>
                            <li class="text-sm fw-semibold">- Hệ thống chỉ là nơi trung gian, tiền sẽ được nhà mạng duyệt
                                thẻ và trừ phí vì vậy Admin sẽ không giải quyết tranh chấp khi xảy ra vấn đề thẻ sai hoặc
                                đúng.</li>
                            <li class="text-sm fw-semibold">- Thẻ nạp sẽ được nhà mạng duyệt và cộng tiền thường là sau vài
                                phút.</li>
                            <li class="text-sm fw-semibold">- Sau 15p chưa thấy cộng tiền, hãy liên hệ Admin để được hỗ trợ.
                            </li>
                        </ul>
                    </div>
                    <form action="{{ route('guard.recharge-card') }}" method="POST" id="chargin-card">
                        @csrf
                        <div class="row">
                            <div class="col-md-3 form-group mb-3">
                                <label for="card_type">Loại thẻ</label>
                                <select name="card_type" id="card_type" class="form-control">
                                    <option value="VIETTEL">Viettel (Chiết khấu: {{ site('discount_card') }}%) </option>
                                    <option value="MOBIFONE">Mobifone (Chiết khấu: {{ site('discount_card') }}%) </option>
                                    <option value="VINAPHONE">Vinaphone (Chiết khấu: {{ site('discount_card') }}%) </option>
                                    <option value="VIETNAMOBILE">Vietnamobile (Chiết khấu: {{ site('discount_card') }}%)
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3 form-group mb-3">
                                <label for="card_value">Mệnh giá</label>
                                <select name="card_value" id="card_value" class="form-control">
                                    <option value="10000">10,000 VNĐ</option>
                                    <option value="20000">20,000 VNĐ</option>
                                    <option value="30000">30,000 VNĐ</option>
                                    <option value="50000">50,000 VNĐ</option>
                                    <option value="100000">100,000 VNĐ</option>
                                    <option value="200000">200,000 VNĐ</option>
                                    <option value="300000">300,000 VNĐ</option>
                                    <option value="500000">500,000 VNĐ</option>
                                </select>
                            </div>
                            <div class="col-md-3 form-group mb-3">
                                <label for="card_serial">Seri thẻ</label>
                                <input type="text" name="card_serial" id="card_serial" class="form-control">
                            </div>
                            <div class="col-md-3 form-group mb-3">
                                <label for="card_code">Mã thẻ</label>
                                <input type="text" name="card_code" id="card_code" class="form-control">
                            </div>
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary w-100">Nạp thẻ</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary">

                    <h4 class="card-title">Lịch Sử Nạp Thẻ</h4>

                    </div>
                <div class="card-body">
                     
                    <div class="app-datatable-default overflow-auto">
                        <table class="display w-100 row-callback-datatable" id="table-recharge-cards">
                            <thead class="">
                                <tr>
                                    <th>#</th>
                                    <th>Nhà mạng</th>
                                    <th>Số Serial</th>
                                    <th>Mã thẻ</th>
                                    <th>Mệnh giá</th>
                                    <th>Thực nhận</th>
                                    <th>Trạng thái</th>
                                    <th>Thời gian</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('script')
    <script>
        $(document).ready(function() {
            loadTable('#table-recharge-cards', 'recharge-cards', [{
                    data: 'id',
                    name: 'id'
                },
                {
                    data: 'provider',
                    name: 'provider'
                },
                {
                    data: 'serial',
                    name: 'serial'
                },
                {
                    data: 'pin',
                    name: 'pin'
                },
                {
                    data: 'amount_card',
                    name: 'amount_card',

                },
                {
                    data: 'real_amount',
                    name: 'real_amount',
                    render: function(data) {
                        return data ? data?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") + ' VNĐ' :
                            '0 VNĐ';
                    }
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data) {
                        if (data == 'pending') {
                            return '<span class="badge bg-warning">Chờ xử lý</span>';
                        } else if (data == 'success') {
                            return '<span class="badge bg-success">Thẻ đúng</span>';
                        } else if (data == 'wrong') {
                            return '<span class="badge bg-info">Thẻ sai mệnh giá</span>';
                        } else {
                            return '<span class="badge bg-danger">Thất bại</span>';
                        }
                    }
                },
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data) {
                        return moment(data).format('DD/MM/YYYY HH:mm:ss');
                    }
                }
            ]);
        });
    </script>
    <script>
        $(document).ready(function() {
            $('#chargin-card').submit(function(e) {
                e.preventDefault();
                let form = $(this);
                let url = form.attr('action');
                let data = form.serialize();
                let buttonText = form.find('button[type="submit"]').text();

                Swal.fire({
                    title: 'Xác nhận nạp thẻ',
                    text: "Bạn có chắc chắn muốn nạp thẻ này không?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Xác nhận',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: data,
                            dataType: 'JSON',
                            beforeSend: function() {
                                form.find('button[type="submit"]').html(
                                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang nạp...'
                                ).attr('disabled', true);
                            },

                            complete: function() {
                                form.find('button[type="submit"]').text(buttonText)
                                    .attr('disabled', false);
                            },
                            success: function(response) {
                                if (response.status == 'success') {
                                    Swal.fire(
                                        'Thành công!',
                                        response.message,
                                        'success'
                                    );
                                    form.trigger('reset');
                                } else {
                                    Swal.fire(
                                        'Thất bại!',
                                        response.message,
                                        'error'
                                    );
                                }
                            },
                            error: function(xhr) {
                                let errors = xhr.responseJSON.errors;
                                let firstItem = Object.values(errors)[0][0];
                                Swal.fire(
                                    'Thất bại!',
                                    firstItem,
                                    'error'
                                );
                            },
                        });
                    }
                });


            });
        });
    </script>
@endsection
@extends('layouts.guard.guard')
@section('title', 'Nạp tiền')
@section('content')
    <div class="row">
        <div class="col-md-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ route('guard.recharge') }}" class="btn btn-primary w-100  rounded-2">
                                <i class="fa-solid fa-credit-card fa-fw"></i>
                                Ngân hàng
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ route('guard.recharge-card') }}" class="btn btn-outline-primary w-100  rounded-2"><i
                                    class="fa-solid fa-brands fa-cc-mastercard fa-fw"></i> Thẻ cào</a>
                        </div>
                    </div>
                    <div class="alert alert-light-danger mb-3">
                        <ul>
                            <li class="text-sm fw-semibold">- Copy thông tin bên dưới hoặc chụp quét mã QR hệ thống cung
                                cấp.</li>
                            <li class="text-sm fw-semibold">- Vui lòng chuyển khoản đúng nội dung và nạp từ 10.000đ đổ lên.
                            </li>
                            <li class="text-sm fw-semibold">- Hệ thống sẽ tự động cộng tiền vào tài khoản sau vài phút.</li>
                            <li class="text-sm fw-semibold">- Nạp dưới số tiền tối thiểu không xử lý, nạp sai nội dung trừ
                                10%.</li>
                            <li class="text-sm fw-semibold">- Sau 15p nếu chưa thấy cộng tiền, hãy liên hệ Admin để được hỗ
                                trợ.</li>
                        </ul>
                    </div>

                    <div class="row mb-3">
                        @foreach ($accountBanks as $accountBank)
                            <div class="col-md-6 col-lg-6">
                                <div class="card border shadow">
                                    <div class="card-body">
                                        <div class="text-center mb-3">
                                            <img src="{{ asset($accountBank->bank_logo) }}"
                                                alt="{{ $accountBank->bank_name }}" class="img-fluid"
                                                style="max-width: 100px;">
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 d-flex align-items-center">
                                                <ul class="list-unstyled w-100">
                                                    <li class="text-sm">Ngân hàng:
                                                        <strong>{{ $accountBank->bank_name }}</strong>
                                                    </li>
                                                    <li class="text-sm">Chủ tài khoản:
                                                        <strong onclick="copyData('{{ $accountBank->account_name }}')"
                                                            class="text-gray-900">
                                                            {{ $accountBank->account_name }}
                                                        </strong>
                                                    </li>
                                                    <li class="text-sm">Số tài khoản:
                                                        <strong onclick="copyData('{{ $accountBank->account_number }}')"
                                                            class="text-primary">
                                                            {{ $accountBank->account_number }} <i
                                                                class="ri-file-copy-2-fill"></i>
                                                        </strong>
                                                    </li>
                                                    <li class="text-sm">Nạp tối thiểu:
                                                        <strong onclick="copyData('{{ site('min_recharge') }}')"
                                                            class="text-gray-900">
                                                            {{ number_format(site('min_recharge')) }} VNĐ
                                                        </strong>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6 d-flex align-items-center justify-content-center">
                                                <img src="{{ $accountBank->getQrCode(site('transfer_code') . Auth::user()->id) }}"
                                                    alt="{{ $accountBank->bank_name }}" class="img-fluid"
                                                    style="max-width: 150px;">
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="py-2 text-center bg-primary text-white" style="border-radius: 10px 10px 0 0">
                        Nội dung nạp tiền (Vui lòng chú ý nội dung nạp tiền có thể thay đổi thất thường)
                    </div>
                    <div class="border-primary border bg-light-primary py-3" style="border-radius: 0 0 10px 10px">
                        <div class="text-center cursor-pointer"
                            onclick="copyData('{{ site('transfer_code') . Auth::user()->id }}')">
                            <strong class="text-primary fs-3">{{ site('transfer_code') . Auth::user()->id }} <i class="fas fa-copy"></i></strong>
                            <div class="text-xs fw-semibold">Nhấn vào để sao chép</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary">

                    <h4 class="card-title">{{ ucwords('Lịch sử nạp tiền') }}</h4>

                </div>
                <div class="card-body">

                    <div class="app-datatable-default overflow-auto">
                        <table class="display w-100 row-callback-datatable" id="table-recharges">
                            <thead class="">
                                <tr>
                                    <th>#</th>
                                    <th>Thao tác</th>
                                    <th>Mã giao dịch</th>
                                    <th>Ngân hàng</th>
                                    <th>Số tiền</th>
                                    <th>Trạng thái</th>
                                    <th>Nội dung</th>
                                    <th>Thời gian</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('script')
    <script>
        $(document).ready(function() {
            loadTable('#table-recharges', 'recharges', [{
                    data: 'id',
                    name: 'id'
                },
                {
                    data: null,
                    render: function(data) {
                        return data.type == 'bill' ? `

                        ` : '';
                    }
                }, {
                    data: 'trans_code',
                    name: 'trans_code'
                }, {
                    data: 'method',
                    name: 'method'
                }, {
                    data: 'amount',
                    name: 'amount'
                }, {
                    data: 'status',
                    name: 'status',
                    render: function(data) {
                        return data == 'pending' ? '<span class="badge bg-warning">Đang chờ</span>' :
                            '<span class="badge bg-success">Thành công</span>';
                    }
                }, {
                    data: 'description',
                    name: 'description',
                }, {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data) {
                        return moment(data).format('DD/MM/YYYY HH:mm:ss');
                    }
                }
            ])
        })
    </script>
@endsection
