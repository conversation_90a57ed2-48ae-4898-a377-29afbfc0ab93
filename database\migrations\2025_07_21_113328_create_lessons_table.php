<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lessons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chapter_id')->constrained()->onDelete('cascade'); // Thuộc Chương Nào
            $table->string('title'); // Tên <PERSON>i <PERSON>
            $table->string('youtube_link')->nullable(); // Link Video YouTube
            $table->string('file_path')->nullable(); // Đường Dẫn File Tài <PERSON>u
            $table->integer('order')->default(0); // Thứ Tự Bài Học
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lessons');
    }
};
