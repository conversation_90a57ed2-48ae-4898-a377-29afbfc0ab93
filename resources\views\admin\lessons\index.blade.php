<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-video" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.lessons.create') }}"
               style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#1d4ed8'"
               onmouseout="this.style.backgroundColor='#2563eb'">
                <i class="fas fa-plus" style="margin-right: 8px;"></i>
                <PERSON>hê<PERSON><PERSON>
            </a>
        </div>
    </x-slot>

    <!-- Success Message -->
    @if(session('success'))
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; border-radius: 8px; padding: 16px; margin-bottom: 24px; color: #15803d;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            {{ session('success') }}
        </div>
    @endif

    <!-- Lessons List -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        @if($lessons->count() > 0)
            <!-- Table Header -->
            <div style="background-color: #f8fafc; padding: 16px 24px; border-bottom: 1px solid #e5e7eb;">
                <div style="display: grid; grid-template-columns: 60px 180px 180px 1fr 100px 120px 150px; gap: 16px; align-items: center; font-weight: 600; color: #374151; font-size: 0.875rem;">
                    <div>STT</div>
                    <div>
                        <i class="fas fa-book" style="margin-right: 4px;"></i>
                        Môn Học
                    </div>
                    <div>
                        <i class="fas fa-list-alt" style="margin-right: 4px;"></i>
                        Chương
                    </div>
                    <div>Thông Tin Bài Học</div>
                    <div style="text-align: center;">
                        <i class="fas fa-sort-numeric-up" style="margin-right: 4px;"></i>
                        Thứ Tự
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-file-alt" style="margin-right: 4px;"></i>
                        Nội Dung
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-cogs" style="margin-right: 4px;"></i>
                        Thao Tác
                    </div>
                </div>
            </div>

            <!-- Table Body -->
            <div>
                @foreach($lessons as $index => $lesson)
                    <div style="padding: 20px 24px; border-bottom: 1px solid #f3f4f6; {{ $loop->last ? 'border-bottom: none;' : '' }}">
                        <div style="display: grid; grid-template-columns: 60px 180px 180px 1fr 100px 120px 150px; gap: 16px; align-items: center;">
                            <!-- STT -->
                            <div style="font-weight: 600; color: #6b7280;">
                                {{ $index + 1 }}
                            </div>

                            <!-- Môn Học -->
                            <div>
                                <div style="background-color: #dbeafe; color: #2563eb; padding: 6px 10px; border-radius: 6px; font-size: 0.75rem; font-weight: 600; text-align: center;">
                                    <i class="fas fa-book" style="margin-right: 4px;"></i>
                                    {{ Str::limit($lesson->chapter->subject->title, 15) }}
                                </div>
                            </div>

                            <!-- Chương -->
                            <div>
                                <div style="background-color: #f3f4f6; color: #374151; padding: 6px 10px; border-radius: 6px; font-size: 0.75rem; font-weight: 600; text-align: center;">
                                    <i class="fas fa-list-alt" style="margin-right: 4px;"></i>
                                    {{ Str::limit($lesson->chapter->title, 15) }}
                                </div>
                            </div>

                            <!-- Thông Tin Bài Học -->
                            <div>
                                <h3 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 1rem;">
                                    {{ $lesson->title }}
                                </h3>
                                <div style="display: flex; gap: 8px; margin-bottom: 4px;">
                                    @if($lesson->hasVideo())
                                        <span style="background-color: #fef3c7; color: #d97706; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                            <i class="fab fa-youtube" style="margin-right: 2px;"></i>
                                            Video
                                        </span>
                                    @endif
                                    @if($lesson->hasFile())
                                        <span style="background-color: #d1fae5; color: #059669; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                            <i class="fas fa-file-download" style="margin-right: 2px;"></i>
                                            File
                                        </span>
                                    @endif
                                </div>
                                <p style="color: #9ca3af; margin: 0; font-size: 0.75rem;">
                                    <i class="fas fa-calendar-alt" style="margin-right: 4px;"></i>
                                    Tạo: {{ $lesson->created_at->format('d/m/Y H:i') }}
                                </p>
                            </div>

                            <!-- Thứ Tự -->
                            <div style="text-align: center;">
                                <span style="background-color: #f3f4f6; color: #374151; padding: 6px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $lesson->order }}
                                </span>
                            </div>

                            <!-- Nội Dung -->
                            <div style="text-align: center;">
                                <div style="display: flex; flex-direction: column; gap: 4px; align-items: center;">
                                    @if($lesson->hasVideo())
                                        <span style="background-color: #fef3c7; color: #d97706; padding: 4px 8px; border-radius: 6px; font-size: 0.75rem; font-weight: 600;">
                                            <i class="fab fa-youtube" style="margin-right: 4px;"></i>
                                            YouTube
                                        </span>
                                    @endif
                                    @if($lesson->hasFile())
                                        <span style="background-color: #d1fae5; color: #059669; padding: 4px 8px; border-radius: 6px; font-size: 0.75rem; font-weight: 600;">
                                            <i class="fas fa-file-download" style="margin-right: 4px;"></i>
                                            Tài Liệu
                                        </span>
                                    @endif
                                    @if(!$lesson->hasVideo() && !$lesson->hasFile())
                                        <span style="background-color: #fecaca; color: #dc2626; padding: 4px 8px; border-radius: 6px; font-size: 0.75rem; font-weight: 600;">
                                            <i class="fas fa-exclamation-triangle" style="margin-right: 4px;"></i>
                                            Trống
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <!-- Thao Tác -->
                            <div style="display: flex; gap: 8px; justify-content: center;">
                                <a href="{{ route('admin.lessons.show', $lesson) }}"
                                   style="background-color: #f3f4f6; color: #374151; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                                   onmouseout="this.style.backgroundColor='#f3f4f6'"
                                   title="Xem Chi Tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.lessons.edit', $lesson) }}"
                                   style="background-color: #fef3c7; color: #d97706; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#fde68a'"
                                   onmouseout="this.style.backgroundColor='#fef3c7'"
                                   title="Chỉnh Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ route('admin.lessons.destroy', $lesson) }}" style="display: inline; margin: 0;"
                                      onsubmit="return confirm('Bạn Có Chắc Muốn Xóa Bài Học Này?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            style="background-color: #fecaca; color: #dc2626; padding: 8px 12px; border-radius: 6px; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.3s ease;"
                                            onmouseover="this.style.backgroundColor='#fca5a5'"
                                            onmouseout="this.style.backgroundColor='#fecaca'"
                                            title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div style="text-align: center; padding: 60px 20px;">
                <i class="fas fa-video" style="font-size: 64px; color: #d1d5db; margin-bottom: 16px; display: block;"></i>
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #374151; margin: 0 0 8px 0;">
                    Chưa Có Bài Học Nào
                </h3>
                <p style="color: #6b7280; margin: 0 0 24px 0;">
                    Hãy Tạo Bài Học Đầu Tiên Để Bắt Đầu Xây Dựng Nội Dung Khóa Học
                </p>
                <a href="{{ route('admin.lessons.create') }}"
                   style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'"
                   onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-plus" style="margin-right: 8px;"></i>
                    Tạo Bài Học Đầu Tiên
                </a>
            </div>
        @endif
    </div>
</x-admin-layout>