<?php

if (!function_exists('site_title')) {
    function site_title()
    {
        return config('app.name', 'PanDaEdu');
    }
}

if (!function_exists('site_description')) {
    function site_description()
    {
        return 'H<PERSON>ống <PERSON>n <PERSON> - <PERSON>, <PERSON><PERSON><PERSON><PERSON> Mỗi <PERSON>';
    }
}

if (!function_exists('site_keywords')) {
    function site_keywords()
    {
        return 'học tập, gi<PERSON><PERSON> d<PERSON>, kh<PERSON><PERSON> học online, PanDaEdu, học lập trình';
    }
}

if (!function_exists('site_author')) {
    function site_author()
    {
        return 'PanDaEdu Team';
    }
}

if (!function_exists('site_og_image')) {
    function site_og_image()
    {
        return asset('images/og-image.jpg');
    }
}

if (!function_exists('header_scripts')) {
    function header_scripts()
    {
        return '';
    }
}

if (!function_exists('footer_scripts')) {
    function footer_scripts()
    {
        return '';
    }
}

if (!function_exists('site')) {
    function site($key, $default = null)
    {
        // G<PERSON><PERSON> lập function site() từ README.md
        $settings = [
            'partner_key' => 'test_partner_key',
            'partner_id' => 'test_partner_id',
            'transfer_code' => 'pandaedu',
            'min_recharge' => 10000,
            'recharge_promotion' => 10,
            'start_promotion' => now()->subDays(30),
            'end_promotion' => now()->addDays(30),
            'commission' => 5,
            'discount_card' => 5,
            'webhook_discord' => 'https://discord.com/api/webhooks/test',
        ];
        
        return $settings[$key] ?? $default;
    }
}

if (!function_exists('charginCard')) {
    function charginCard($cardType, $cardCode, $cardSerial, $cardValue, $requestId, $partnerId, $sign, $action = 'charge')
    {
        // Giả lập function charginCard() từ README.md
        return [
            'status' => 99,
            'message' => 'Đang xử lý',
            'request_id' => $requestId,
            'trans_id' => 'TRANS_' . time(),
        ];
    }
}

if (!function_exists('sendMessageDis')) {
    function sendMessageDis($webhookUrl, $payload)
    {
        // Giả lập function sendMessageDis() từ README.md
        return true;
    }
}
