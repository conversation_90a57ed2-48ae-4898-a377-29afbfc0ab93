@tailwind base;
@tailwind components;
@tailwind utilities;

/* <PERSON><PERSON><PERSON><PERSON> T<PERSON>y Chỉnh - <PERSON><PERSON><PERSON> */
:root {
    --primary-blue: #2563eb;
    --primary-white: #ffffff;
    --primary-black: #000000;
    --light-gray: #f8fafc;
    --border-gray: #e5e7eb;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* <PERSON><PERSON> */
.btn-primary {
    background-color: var(--primary-blue);
    color: var(--primary-white);
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    text-align: center;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* <PERSON><PERSON> */
.btn-secondary {
    background-color: var(--light-gray);
    color: var(--primary-black);
    padding: 12px 24px;
    border-radius: 8px;
    border: 2px solid var(--border-gray);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    text-align: center;
}

.btn-secondary:hover {
    background-color: var(--primary-white);
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

/* Card */
.card {
    background-color: var(--primary-white);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-gray);
    margin-bottom: 20px;
}

/* Input Fields */
.input-field {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-gray);
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: var(--primary-white);
}

.input-field:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Responsive Grid */
.grid-responsive {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
