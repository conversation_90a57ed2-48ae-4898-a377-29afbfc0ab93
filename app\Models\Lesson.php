<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Lesson extends Model
{
    protected $fillable = [
        'chapter_id',
        'title',
        'youtube_link',
        'file_path',
        'order',
    ];

    /**
     * <PERSON><PERSON><PERSON>ra <PERSON>ó Video YouTube Không
     */
    public function hasVideo()
    {
        return !empty($this->youtube_link);
    }

    /**
     * Kiểm Tra Có File Đính Kèm Không
     */
    public function hasFile()
    {
        return !empty($this->file_path);
    }

    /**
     * Lấy URL File
     */
    public function getFileUrl()
    {
        return $this->file_path ? asset('storage/' . $this->file_path) : null;
    }

    /**
     * Lấy Tên File
     */
    public function getFileName()
    {
        return $this->file_path ? basename($this->file_path) : null;
    }

    /**
     * Relationship: Một Bà<PERSON> Chương
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    /**
     * Relationship: <PERSON><PERSON>t <PERSON> (Thông Q<PERSON> Chương)
     */
    public function subject()
    {
        return $this->hasOneThrough(Subject::class, Chapter::class, 'id', 'id', 'chapter_id', 'subject_id');
    }

    /**
     * Lấy YouTube Video ID Từ Link
     */
    public function getYoutubeIdAttribute(): ?string
    {
        if (!$this->youtube_link) {
            return null;
        }

        // Extract YouTube ID từ các format khác nhau
        preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $this->youtube_link, $matches);

        return $matches[1] ?? null;
    }

    /**
     * Lấy YouTube Embed URL
     */
    public function getEmbedUrl(): ?string
    {
        $youtubeId = $this->youtube_id;

        if (!$youtubeId) {
            return null;
        }

        return "https://www.youtube.com/embed/{$youtubeId}";
    }

    /**
     * Relationship: Lesson có nhiều Comments
     */
    public function comments()
    {
        return $this->hasMany(LessonComment::class)->with('user', 'replies');
    }

    /**
     * Relationship: Lesson có nhiều Progress
     */
    public function progress()
    {
        return $this->hasMany(UserLessonProgress::class);
    }

    /**
     * Kiểm tra user đã hoàn thành bài học chưa
     */
    public function isCompletedByUser($userId): bool
    {
        return $this->progress()
            ->where('user_id', $userId)
            ->where('is_completed', true)
            ->exists();
    }

    /**
     * Lấy progress của user cho bài học này
     */
    public function getProgressForUser($userId)
    {
        return $this->progress()
            ->where('user_id', $userId)
            ->first();
    }


}
