<x-admin-layout>
    <x-slot name="header">
        <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
            Admin Dashboard
        </h2>
    </x-slot>
        <!-- Welcome Card -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 32px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; margin-bottom: 32px;">
            <h2 style="font-size: 2rem; font-weight: bold; color: #000000; margin: 0 0 16px 0;">
                <i class="fas fa-user-cog" style="margin-right: 12px; color: #dc2626;"></i>
                Ch<PERSON>o Mừng Đến Với Admin Dashboard
            </h2>
            <p style="color: #6b7280; font-size: 1.1rem; margin: 0;">
                <PERSON><PERSON><PERSON>. B<PERSON>ống Từ Đây.
            </p>
        </div>

        <!-- Stats Cards -->
        <div style="display: grid; gap: 20px; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); margin-bottom: 32px;">
            <!-- Total Users -->
            <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                            Tổng Số User
                        </h3>
                        <p style="font-size: 2rem; font-weight: bold; color: #2563eb; margin: 0;">
                            {{ \App\Models\User::count() }}
                        </p>
                    </div>
                    <div style="width: 48px; height: 48px; background-color: #dbeafe; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-users" style="font-size: 20px; color: #2563eb;"></i>
                    </div>
                </div>
            </div>

            <!-- Admin Count -->
            <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                            Số Admin
                        </h3>
                        <p style="font-size: 2rem; font-weight: bold; color: #059669; margin: 0;">
                            {{ \App\Models\User::where('role', 'admin')->count() }}
                        </p>
                    </div>
                    <div style="width: 48px; height: 48px; background-color: #d1fae5; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-user-cog" style="font-size: 20px; color: #059669;"></i>
                    </div>
                </div>
            </div>

            <!-- Regular Users -->
            <div style="background-color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; margin: 0 0 8px 0; text-transform: uppercase;">
                            User Thường
                        </h3>
                        <p style="font-size: 2rem; font-weight: bold; color: #7c3aed; margin: 0;">
                            {{ \App\Models\User::where('role', 'user')->count() }}
                        </p>
                    </div>
                    <div style="width: 48px; height: 48px; background-color: #ede9fe; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-user" style="font-size: 20px; color: #7c3aed;"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div style="background-color: #ffffff; border-radius: 12px; padding: 32px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb;">
            <h3 style="font-size: 1.25rem; font-weight: bold; color: #000000; margin: 0 0 24px 0;">
                <i class="fas fa-bolt" style="margin-right: 8px; color: #f59e0b;"></i>
                Thao Tác Nhanh
            </h3>
            <div style="display: grid; gap: 16px; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                <a href="{{ route('admin.subjects.index') }}"
                   style="background-color: #2563eb; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.backgroundColor='#2563eb'; this.style.transform='translateY(0)'">
                    <i class="fas fa-book" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                    Quản Lý Môn Học
                </a>
                <a href="#"
                   style="background-color: #059669; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
                   onmouseover="this.style.backgroundColor='#047857'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.backgroundColor='#059669'; this.style.transform='translateY(0)'">
                    <i class="fas fa-users" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                    Quản Lý User
                </a>
                <a href="#"
                   style="background-color: #7c3aed; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
                   onmouseover="this.style.backgroundColor='#6d28d9'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.backgroundColor='#7c3aed'; this.style.transform='translateY(0)'">
                    <i class="fas fa-chart-bar" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                    Xem Thống Kê
                </a>
                <a href="#"
                   style="background-color: #dc2626; color: #ffffff; padding: 20px; border-radius: 12px; text-decoration: none; font-weight: 600; text-align: center; transition: all 0.3s ease; display: block;"
                   onmouseover="this.style.backgroundColor='#b91c1c'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.backgroundColor='#dc2626'; this.style.transform='translateY(0)'">
                    <i class="fas fa-cogs" style="font-size: 24px; display: block; margin-bottom: 8px;"></i>
                    Cài Đặt Hệ Thống
                </a>
            </div>
</x-admin-layout>
