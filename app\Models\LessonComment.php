<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LessonComment extends Model
{
    protected $fillable = [
        'lesson_id',
        'user_id',
        'content',
        'parent_id',
    ];

    /**
     * Relationship: Comment thuộc về User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relationship: Comment thuộc về Lesson
     */
    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Relationship: Comment cha
     */
    public function parent()
    {
        return $this->belongsTo(LessonComment::class, 'parent_id');
    }

    /**
     * Relationship: Comment con
     */
    public function replies()
    {
        return $this->hasMany(LessonComment::class, 'parent_id')->with('user', 'replies');
    }

    /**
     * Scope: Chỉ lấy comment gốc (không phải reply)
     */
    public function scopeParentComments($query)
    {
        return $query->whereNull('parent_id');
    }
}
