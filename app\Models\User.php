<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'balance',
        'total_deposit',
        'total_spent',
        'ref_code',
        'ref_by',
        'domain',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'balance' => 'decimal:2',
            'total_deposit' => 'decimal:2',
            'total_spent' => 'decimal:2',
        ];
    }

    /**
     * Kiểm Tra Xem User Có Phải Admin Không
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Relationship: User Có Nhiều UserSubject (Môn Học Đã Mua)
     */
    public function userSubjects()
    {
        return $this->hasMany(UserSubject::class);
    }

    /**
     * Relationship: User Có Nhiều Subjects Đã Mua (Thông Qua UserSubject)
     */
    public function purchasedSubjects()
    {
        return $this->belongsToMany(Subject::class, 'user_subjects')
            ->withPivot(['amount_paid', 'payment_method', 'transaction_code', 'status', 'purchased_at', 'expires_at'])
            ->withTimestamps();
    }

    /**
     * Kiểm Tra User Đã Mua Subject Nào Đó Chưa
     */
    public function hasPurchasedSubject($subjectId): bool
    {
        return $this->userSubjects()
            ->bySubject($subjectId)
            ->completed()
            ->byDomain()
            ->exists();
    }

    /**
     * Kiểm Tra User Có Thể Truy Cập Subject Nào Đó Không
     */
    public function canAccessSubject($subjectId): bool
    {
        $subject = Subject::find($subjectId);

        if (!$subject) {
            return false;
        }

        return $subject->canUserAccess($this->id);
    }

    /**
     * Lấy Danh Sách Subjects Đã Mua Và Có Thể Truy Cập
     */
    public function getAccessibleSubjects()
    {
        return $this->userSubjects()
            ->completed()
            ->byDomain()
            ->with('subject')
            ->get()
            ->filter(function ($userSubject) {
                return $userSubject->canAccess();
            })
            ->pluck('subject');
    }

    /**
     * Mua Subject
     */
    public function purchaseSubject($subjectId, $amountPaid, $paymentMethod = null, $transactionCode = null)
    {
        $subject = Subject::find($subjectId);

        if (!$subject) {
            throw new \Exception('Môn Học Không Tồn Tại');
        }

        if ($subject->isFree()) {
            throw new \Exception('Môn Học Này Miễn Phí, Không Cần Mua');
        }

        if ($this->hasPurchasedSubject($subjectId)) {
            throw new \Exception('Bạn Đã Mua Môn Học Này Rồi');
        }

        return $subject->createPurchaseForUser($this->id, $amountPaid, $paymentMethod, $transactionCode);
    }

    /**
     * Kiểm Tra Xem User Có Phải User Thường Không
     */
    public function isUser(): bool
    {
        return $this->role === 'user';
    }
}
