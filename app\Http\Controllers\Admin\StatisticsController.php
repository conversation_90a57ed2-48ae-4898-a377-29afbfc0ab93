<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Subject;
use App\Models\Chapter;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Carbon\Carbon;

class StatisticsController extends Controller
{
    /**
     * Hiển Thị Trang Thống Kê Tổng Quan
     */
    public function index()
    {
        // Thống kê tổng quan
        $totalUsers = User::count();
        $totalAdmins = User::where('is_admin', true)->count();
        $totalSubjects = Subject::count();
        $totalChapters = Chapter::count();
        $totalLessons = Lesson::count();
        $verifiedUsers = User::whereNotNull('email_verified_at')->count();

        // Thống kê theo thời gian (30 ngày gần nhất)
        $userRegistrations = $this->getUserRegistrationStats();
        $contentCreationStats = $this->getContentCreationStats();

        // Top subjects với nhiều chương và bài học nhất
        $topSubjects = Subject::withCount(['chapters', 'lessons'])
            ->orderBy('lessons_count', 'desc')
            ->take(5)
            ->get();

        // Thống kê theo tháng
        $monthlyStats = $this->getMonthlyStats();

        return view('admin.statistics.index', compact(
            'totalUsers',
            'totalAdmins',
            'totalSubjects',
            'totalChapters',
            'totalLessons',
            'verifiedUsers',
            'userRegistrations',
            'contentCreationStats',
            'topSubjects',
            'monthlyStats'
        ));
    }

    /**
     * Thống kê đăng ký người dùng 30 ngày gần nhất
     */
    private function getUserRegistrationStats()
    {
        $stats = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $count = User::whereDate('created_at', $date)->count();
            $stats[] = [
                'date' => $date->format('d/m'),
                'count' => $count
            ];
        }
        return $stats;
    }

    /**
     * Thống kê tạo nội dung 30 ngày gần nhất
     */
    private function getContentCreationStats()
    {
        $stats = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $subjects = Subject::whereDate('created_at', $date)->count();
            $chapters = Chapter::whereDate('created_at', $date)->count();
            $lessons = Lesson::whereDate('created_at', $date)->count();

            $stats[] = [
                'date' => $date->format('d/m'),
                'subjects' => $subjects,
                'chapters' => $chapters,
                'lessons' => $lessons
            ];
        }
        return $stats;
    }

    /**
     * Thống kê theo tháng (12 tháng gần nhất)
     */
    private function getMonthlyStats()
    {
        $stats = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $startOfMonth = $date->copy()->startOfMonth();
            $endOfMonth = $date->copy()->endOfMonth();

            $users = User::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();
            $subjects = Subject::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();
            $chapters = Chapter::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();
            $lessons = Lesson::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();

            $stats[] = [
                'month' => $date->format('m/Y'),
                'users' => $users,
                'subjects' => $subjects,
                'chapters' => $chapters,
                'lessons' => $lessons
            ];
        }
        return $stats;
    }

    /**
     * API endpoint cho biểu đồ
     */
    public function chartData(Request $request)
    {
        $type = $request->get('type', 'users');

        switch ($type) {
            case 'users':
                return response()->json($this->getUserRegistrationStats());
            case 'content':
                return response()->json($this->getContentCreationStats());
            case 'monthly':
                return response()->json($this->getMonthlyStats());
            default:
                return response()->json([]);
        }
    }
}
