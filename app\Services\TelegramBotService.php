<?php

namespace App\Services;

use App\Models\Lesson;
use App\Models\Subject;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramBotService
{
    private $botToken;
    private $apiUrl;

    public function __construct()
    {
        $this->botToken = telegram_bot_token() ?: config('services.telegram.bot_token');
        $this->apiUrl = "https://api.telegram.org/bot{$this->botToken}";
    }

    /**
     * G<PERSON>i tin nhắn đến nhóm Telegram
     */
    public function sendMessage($chatId, $message, $parseMode = 'HTML')
    {
        try {
            $response = Http::post("{$this->apiUrl}/sendMessage", [
                'chat_id' => $chatId,
                'text' => $message,
                'parse_mode' => $parseMode,
                'disable_web_page_preview' => true
            ]);

            if ($response->successful()) {
                Log::info('Telegram message sent successfully', [
                    'chat_id' => $chatId,
                    'message' => substr($message, 0, 100) . '...'
                ]);
                return $response->json();
            } else {
                Log::error('Failed to send Telegram message', [
                    'chat_id' => $chatId,
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Telegram API error', [
                'error' => $e->getMessage(),
                'chat_id' => $chatId
            ]);
            return false;
        }
    }

    /**
     * Thông báo bài học mới
     */
    public function notifyNewLesson(Lesson $lesson)
    {
        $subject = $lesson->chapter->subject;
        
        if (!$subject->telegram_group_id || !$subject->auto_notify) {
            return false;
        }

        $message = $this->formatNewLessonMessage($lesson);
        return $this->sendMessage($subject->telegram_group_id, $message);
    }

    /**
     * Format Tin Nhắn Bài Học Mới
     */
    private function formatNewLessonMessage(Lesson $lesson)
    {
        $subject = $lesson->chapter->subject;
        $chapter = $lesson->chapter;

        $message = "<b>Có Bài Học Mới Nè Bạn Ơi</b>\n\n";
        $message .= "<b>Môn Học:</b> {$subject->title}\n";
        $message .= "<b>Chương:</b> {$chapter->title}\n";
        $message .= "<b>Bài Học:</b> {$lesson->title}\n\n";

        if ($lesson->hasVideo()) {
            $message .= "<b>Video:</b> Có\n";
        }

        if ($lesson->hasDocument()) {
            $message .= "<b>Tài Liệu:</b> Có\n";
        }

        $message .= "\n<b>Truy Cập:</b> " . route('user.subjects.lesson', [$subject, $chapter, $lesson]);

        if ($subject->group_link) {
            $message .= "\n<b>Nhóm Học:</b> {$subject->group_link}";
        }

        return $message;
    }

    /**
     * Gửi Thông Báo Nhắc Nhở Học Tập
     */
    public function sendStudyReminder($chatId, $subject)
    {
        $message = "<b>Nhắc Nhở Học Tập </b>\n\n";
        $message .= "<b>Môn Học:</b> {$subject->title}\n";
        $message .= "Hôm Nay Bạn Đã Học Bài Chưa?\n\n";
        $message .= "Hãy Dành Ít Nhất 30 Phút Để Học Tập Mỗi Ngày!\n";
        $message .= "<b>Truy Cập:</b> " . route('user.subjects.show', $subject);

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Xử lý webhook từ Telegram
     */
    public function handleWebhook($update)
    {
        if (!isset($update['message'])) {
            return false;
        }

        $message = $update['message'];
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';
        $userId = $message['from']['id'] ?? null;

        // Xử lý các lệnh bot
        if (strpos($text, '/') === 0) {
            return $this->handleCommand($chatId, $text, $userId);
        }

        return false;
    }

    /**
     * Xử lý các lệnh bot
     */
    private function handleCommand($chatId, $command, $userId)
    {
        $parts = explode(' ', $command);
        $cmd = strtolower($parts[0]);

        switch ($cmd) {
            case '/start':
                return $this->handleStartCommand($chatId);
            
            case '/bai':
                return $this->handleLessonsCommand($chatId);
            
            case '/tai':
                $lessonId = $parts[1] ?? null;
                return $this->handleDownloadCommand($chatId, $lessonId);
            
            case '/ho_tro':
                return $this->handleSupportCommand($chatId);

            case '/cong_dong':
                return $this->handleCommunityCommand($chatId);

            default:
                return $this->handleUnknownCommand($chatId);
        }
    }

    /**
     * Lệnh /start
     */
    private function handleStartCommand($chatId)
    {
        $message = "<b>" . bot_welcome_message() . "</b>\n\n";
        $message .= "<b>Các Lệnh Có Sẵn:</b>\n";
        $message .= "/bai - Xem Danh Sách Bài Học Mới Nhất\n";
        $message .= "/tai [ID] - Tải Tài Liệu Bài Học\n";
        $message .= "/ho_tro - Liên Hệ Hỗ Trợ\n";
        $message .= "/cong_dong - Link Cộng Đồng Chung\n\n";
        $message .= "Chúc Bạn Học Tập Hiệu Quả!";

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Lệnh /bai - Danh Sách Bài Học Mới Nhất
     */
    private function handleLessonsCommand($chatId)
    {
        $lessons = Lesson::with(['chapter.subject'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        if ($lessons->isEmpty()) {
            $message = "Hiện Tại Chưa Có Bài Học Mới Nào.";
        } else {
            $message = "<b>Bài Học Mới Nhất</b>\n\n";

            foreach ($lessons as $lesson) {
                $message .= "<b>{$lesson->title}</b>\n";
                $message .= "   Chương: {$lesson->chapter->title}\n";
                $message .= "   Môn Học: {$lesson->chapter->subject->title}\n";

                if ($lesson->hasDocument()) {
                    $message .= "   /tai {$lesson->id} - Tải Tài Liệu\n";
                }

                $message .= "\n";
            }
        }

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Lệnh /tai - Tải Tài Liệu
     */
    private function handleDownloadCommand($chatId, $lessonId)
    {
        if (!$lessonId) {
            $message = "Vui Lòng Cung Cấp ID Bài Học.\nVí Dụ: /tai 123";
            return $this->sendMessage($chatId, $message);
        }

        $lesson = Lesson::find($lessonId);

        if (!$lesson) {
            $message = "Không Tìm Thấy Bài Học Với ID: {$lessonId}";
            return $this->sendMessage($chatId, $message);
        }

        if (!$lesson->hasDocument()) {
            $message = "Bài Học Này Không Có Tài Liệu Đính Kèm.";
            return $this->sendMessage($chatId, $message);
        }

        $downloadUrl = route('user.documents.download', $lesson);
        $message = "<b>Tài Liệu: {$lesson->title}</b>\n\n";
        $message .= "<b>Link Tải:</b> {$downloadUrl}\n\n";
        $message .= "<i>Click Vào Link Để Tải Tài Liệu</i>";

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Lệnh /ho_tro - Hỗ Trợ
     */
    private function handleSupportCommand($chatId)
    {
        $message = "<b>Hỗ Trợ Học Tập</b>\n\n";
        $message .= bot_help_message() . "\n\n";
        $message .= "<b>Liên Hệ:</b>\n";
        $message .= "Email: " . (contact_email() ?: '<EMAIL>') . "\n";

        if (contact_phone()) {
            $message .= "Điện Thoại: " . contact_phone() . "\n";
        }

        $message .= "\n<b>Hoặc Gửi Tin Nhắn Trực Tiếp Tại Đây, Admin Sẽ Phản Hồi Sớm Nhất!</b>";

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Lệnh /cong_dong - Cộng Đồng
     */
    private function handleCommunityCommand($chatId)
    {
        $message = "<b>Cộng Đồng Học Tập</b>\n\n";

        if (telegram_community_group()) {
            $message .= "<b>Telegram Cộng Đồng:</b>\n";
            $message .= telegram_community_group() . "\n\n";
        }

        if (discord_community_server()) {
            $message .= "<b>Discord Server:</b>\n";
            $message .= discord_community_server() . "\n\n";
        }

        if (!telegram_community_group() && !discord_community_server()) {
            $message .= "Hiện Tại Chưa Có Link Cộng Đồng Nào.\n";
            $message .= "Vui Lòng Liên Hệ Admin Để Biết Thêm Chi Tiết.";
        } else {
            $message .= "Tham Gia Cộng Đồng Để Kết Nối Với Các Bạn Học Viên Khác!";
        }

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Lệnh Không Xác Định
     */
    private function handleUnknownCommand($chatId)
    {
        $message = "Lệnh Gõ Không Được Nhận Diện.\n\n";
        $message .= "Vui Lòng Gõ /start Để Xem Danh Sách Lệnh Có Sẵn.";

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Gửi Thống Kê Học Tập Hàng Tuần
     */
    public function sendWeeklyStats($chatId, $subject)
    {
        // Lấy Thống Kê Học Tập Trong Tuần
        $weeklyLessons = Lesson::whereHas('chapter', function($query) use ($subject) {
            $query->where('subject_id', $subject->id);
        })->whereBetween('created_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ])->count();

        $message = "<b>Thống Kê Tuần Này</b>\n\n";
        $message .= "<b>Môn Học:</b> {$subject->title}\n";
        $message .= "<b>Bài Học Mới:</b> {$weeklyLessons} Bài\n";
        $message .= "<b>Mục Tiêu:</b> Hoàn Thành Tất Cả Bài Học\n\n";
        $message .= "Cố Gắng Lên! Tuần Tới Sẽ Tốt Hơn!";

        return $this->sendMessage($chatId, $message);
    }
}
