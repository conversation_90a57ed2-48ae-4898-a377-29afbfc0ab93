<x-admin-layout>
    <x-slot name="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 style="font-size: 1.5rem; font-weight: bold; color: #000000; margin: 0;">
                <i class="fas fa-book" style="margin-right: 8px; color: #2563eb;"></i>
                <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <a href="{{ route('admin.subjects.create') }}"
               style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
               onmouseover="this.style.backgroundColor='#1d4ed8'"
               onmouseout="this.style.backgroundColor='#2563eb'">
                <i class="fas fa-plus" style="margin-right: 8px;"></i>
                Thê<PERSON>
            </a>
        </div>
    </x-slot>

    <!-- Success Message -->
    @if(session('success'))
        <div style="background-color: #dcfce7; border: 1px solid #16a34a; border-radius: 8px; padding: 16px; margin-bottom: 24px; color: #15803d;">
            <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
            {{ session('success') }}
        </div>
    @endif

    <!-- Subjects List -->
    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: 1px solid #e5e7eb; overflow: hidden;">
        @if($subjects->count() > 0)
            <!-- Table Header -->
            <div style="background-color: #f8fafc; padding: 16px 24px; border-bottom: 1px solid #e5e7eb;">
                <div style="display: grid; grid-template-columns: 60px 1fr 120px 100px 100px 120px 120px 180px; gap: 16px; align-items: center; font-weight: 600; color: #374151; font-size: 0.875rem;">
                    <div>STT</div>
                    <div>Thông Tin Môn Học</div>
                    <div style="text-align: center; white-space: nowrap;">
                        <i class="fas fa-chalkboard-teacher" style="margin-right: 4px;"></i>
                        Giáo Viên
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-bookmark" style="margin-right: 4px;"></i>
                        Chương
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-play-circle" style="margin-right: 4px;"></i>
                        Bài Học
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-money-bill-wave" style="margin-right: 4px;"></i>
                        Giá
                    </div>
                    <div style="text-align: center; white-space: nowrap;">
                        <i class="fas fa-toggle-on" style="margin-right: 4px;"></i>
                        Trạng Thái
                    </div>
                    <div style="text-align: center; white-space: nowrap;">
                        <i class="fas fa-cogs" style="margin-right: 4px;"></i>
                        Thao Tác
                    </div>
                </div>
            </div>

            <!-- Table Body -->
            <div>
                @foreach($subjects as $index => $subject)
                    <div style="padding: 20px 24px; border-bottom: 1px solid #f3f4f6; {{ $loop->last ? 'border-bottom: none;' : '' }}">
                        <div style="display: grid; grid-template-columns: 60px 1fr 120px 100px 100px 120px 120px 180px; gap: 16px; align-items: center;">
                            <!-- STT -->
                            <div style="font-weight: 600; color: #6b7280;">
                                {{ $index + 1 }}
                            </div>

                            <!-- Thông Tin Môn Học -->
                            <div style="display: flex; align-items: center; gap: 16px;">
                                @if($subject->thumbnail)
                                    <img src="{{ Storage::url($subject->thumbnail) }}"
                                         alt="{{ $subject->title }}"
                                         style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px; border: 1px solid #e5e7eb;">
                                @else
                                    <div style="width: 60px; height: 60px; background-color: #f3f4f6; border-radius: 8px; display: flex; align-items: center; justify-content: center; border: 1px solid #e5e7eb;">
                                        <i class="fas fa-book" style="font-size: 24px; color: #9ca3af;"></i>
                                    </div>
                                @endif
                                <div>
                                    <h3 style="font-weight: 600; color: #000000; margin: 0 0 4px 0; font-size: 1rem;">
                                        {{ $subject->title }}
                                    </h3>
                                    <p style="color: #6b7280; margin: 0; font-size: 0.875rem; line-height: 1.4;">
                                        {{ Str::limit($subject->description, 80) }}
                                    </p>
                                    <p style="color: #9ca3af; margin: 4px 0 0 0; font-size: 0.75rem;">
                                        <i class="fas fa-calendar-alt" style="margin-right: 4px;"></i>
                                        Tạo: {{ $subject->created_at->format('d/m/Y H:i') }}
                                    </p>
                                </div>
                            </div>

                            <!-- Giáo Viên -->
                            <div style="text-align: center; white-space: nowrap;">
                                @if($subject->instructor_name)
                                    <span style="background-color: #f3e8ff; color: #7c3aed; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                        <i class="fas fa-chalkboard-teacher" style="margin-right: 4px;"></i>
                                        {{ $subject->instructor_name }}
                                    </span>
                                @else
                                    <span style="color: #9ca3af; font-size: 0.75rem;">
                                        <i class="fas fa-minus" style="margin-right: 4px;"></i>
                                        Chưa có
                                    </span>
                                @endif
                            </div>

                            <!-- Số Chương -->
                            <div style="text-align: center;">
                                <span style="background-color: #dbeafe; color: #2563eb; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $subject->chapters_count }}
                                </span>
                            </div>

                            <!-- Số Bài Học -->
                            <div style="text-align: center;">
                                <span style="background-color: #d1fae5; color: #059669; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    {{ $subject->lessons_count }}
                                </span>
                            </div>

                            <!-- Giá -->
                            <div style="text-align: center;">
                                @if($subject->isFree())
                                    <span style="background-color: #dcfce7; color: #16a34a; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                        <i class="fas fa-gift" style="margin-right: 4px;"></i>
                                        Miễn Phí
                                    </span>
                                @else
                                    <span style="background-color: #fef3c7; color: #d97706; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                                        <i class="fas fa-tag" style="margin-right: 4px;"></i>
                                        {{ number_format($subject->price, 0, ',', '.') }}đ
                                    </span>
                                @endif
                            </div>

                            <!-- Trạng Thái -->
                            <div style="text-align: center; white-space: nowrap;">
                                <span style="background-color: #dcfce7; color: #16a34a; padding: 4px 12px; border-radius: 12px; font-size: 0.875rem; font-weight: 600;">
                                    <i class="fas fa-check-circle" style="margin-right: 4px;"></i>
                                    Hoạt Động
                                </span>
                            </div>

                            <!-- Thao Tác -->
                            <div style="display: flex; gap: 8px; justify-content: center; white-space: nowrap;">
                                <a href="{{ route('admin.subjects.show', $subject) }}"
                                   style="background-color: #f3f4f6; color: #374151; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#e5e7eb'"
                                   onmouseout="this.style.backgroundColor='#f3f4f6'"
                                   title="Xem Chi Tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.subjects.edit', $subject) }}"
                                   style="background-color: #fef3c7; color: #d97706; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 0.875rem; transition: all 0.3s ease;"
                                   onmouseover="this.style.backgroundColor='#fde68a'"
                                   onmouseout="this.style.backgroundColor='#fef3c7'"
                                   title="Chỉnh Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ route('admin.subjects.destroy', $subject) }}" style="display: inline; margin: 0;"
                                      onsubmit="return confirm('Bạn Có Chắc Muốn Xóa Môn Học Này? Tất Cả Chương Và Bài Học Sẽ Bị Xóa!')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            style="background-color: #fecaca; color: #dc2626; padding: 8px 12px; border-radius: 6px; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.3s ease;"
                                            onmouseover="this.style.backgroundColor='#fca5a5'"
                                            onmouseout="this.style.backgroundColor='#fecaca'"
                                            title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div style="text-align: center; padding: 60px 20px;">
                <i class="fas fa-book" style="font-size: 64px; color: #d1d5db; margin-bottom: 16px; display: block;"></i>
                <h3 style="font-size: 1.25rem; font-weight: 600; color: #374151; margin: 0 0 8px 0;">
                    Chưa Có Môn Học Nào
                </h3>
                <p style="color: #6b7280; margin: 0 0 24px 0;">
                    Hãy Tạo Môn Học Đầu Tiên Để Bắt Đầu Xây Dựng Khóa Học
                </p>
                <a href="{{ route('admin.subjects.create') }}"
                   style="background-color: #2563eb; color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.backgroundColor='#1d4ed8'"
                   onmouseout="this.style.backgroundColor='#2563eb'">
                    <i class="fas fa-plus" style="margin-right: 8px;"></i>
                    Tạo Môn Học Đầu Tiên
                </a>
            </div>
        @endif
    </div>
</x-admin-layout>